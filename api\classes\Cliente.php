<?php
/**
 * Modelo de Clientes
 * Sistema Saúde Flex - Agendamentos
 */

require_once __DIR__ . '/BaseModel.php';

class Cliente extends BaseModel {
    protected $table = 'clientes';
    
    /**
     * Buscar clientes ativos
     */
    public function findAtivos() {
        return $this->findAll(['status' => 'ativo'], 'nome ASC');
    }
    
    /**
     * Criar novo cliente
     */
    public function create($data) {
        $id = parent::create($data);
        
        if ($id) {
            $this->logAuditoria('CREATE', $id, null, $data);
        }
        
        return $id;
    }
    
    /**
     * Atualizar cliente
     */
    public function update($id, $data) {
        $dadosAnteriores = $this->findById($id);
        $result = parent::update($id, $data);
        
        if ($result) {
            $this->logAuditoria('UPDATE', $id, $dadosAnteriores, $data);
        }
        
        return $result;
    }
    
    /**
     * Excluir cliente (soft delete)
     */
    public function delete($id) {
        $dadosAnteriores = $this->findById($id);
        $result = $this->softDelete($id);
        
        if ($result) {
            $this->logAuditoria('DELETE', $id, $dadosAnteriores);
        }
        
        return $result;
    }
    
    /**
     * Buscar cliente por email
     */
    public function findByEmail($email) {
        return $this->findOne(['email' => $email, 'status' => 'ativo']);
    }
    
    /**
     * Buscar clientes por termo de pesquisa
     */
    public function search($termo) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'ativo' 
                AND (nome LIKE ? OR email LIKE ? OR telefone LIKE ?) 
                ORDER BY nome ASC";
        
        $termoBusca = "%{$termo}%";
        return $this->query($sql, [$termoBusca, $termoBusca, $termoBusca]);
    }
    
    /**
     * Verificar se email já existe
     */
    public function emailExists($email, $excludeId = null) {
        $conditions = ['email' => $email, 'status' => 'ativo'];
        
        if ($excludeId) {
            $sql = "SELECT COUNT(*) FROM {$this->table} WHERE email = ? AND status = 'ativo' AND id != ?";
            return $this->queryValue($sql, [$email, $excludeId]) > 0;
        }
        
        return $this->count($conditions) > 0;
    }
    
    /**
     * Validar dados do cliente
     */
    public function validar($data, $isUpdate = false) {
        $rules = [
            'nome' => ['required' => true, 'max_length' => 255],
            'telefone' => ['max_length' => 20],
            'email' => ['type' => 'email', 'max_length' => 255],
            'endereco' => ['max_length' => 500],
            'cidade' => ['max_length' => 100],
            'estado' => ['max_length' => 2],
            'cep' => ['max_length' => 10],
            'data_nascimento' => ['type' => 'date'],
            'genero' => ['in' => ['masculino', 'feminino', 'outro']]
        ];
        
        $errors = validateInput($data, $rules);
        
        // Verificar se email já existe (se fornecido)
        if (isset($data['email']) && !empty($data['email'])) {
            $excludeId = $isUpdate && isset($data['id']) ? $data['id'] : null;
            if ($this->emailExists($data['email'], $excludeId)) {
                $errors['email'] = 'Este email já está em uso';
            }
        }
        
        return $errors;
    }
    
    /**
     * Buscar estatísticas de clientes
     */
    public function getEstatisticas() {
        $stats = [];
        
        // Total de clientes ativos
        $stats['total_ativos'] = $this->count(['status' => 'ativo']);
        
        // Clientes cadastrados este mês
        $sql = "SELECT COUNT(*) FROM {$this->table} 
                WHERE status = 'ativo' 
                AND MONTH(data_cadastro) = MONTH(CURRENT_DATE()) 
                AND YEAR(data_cadastro) = YEAR(CURRENT_DATE())";
        $stats['cadastrados_mes'] = $this->queryValue($sql);
        
        // Distribuição por gênero
        $sql = "SELECT genero, COUNT(*) as total FROM {$this->table} 
                WHERE status = 'ativo' AND genero IS NOT NULL 
                GROUP BY genero";
        $generos = $this->query($sql);
        
        foreach ($generos as $genero) {
            $stats['por_genero'][$genero['genero']] = $genero['total'];
        }
        
        // Distribuição por estado
        $sql = "SELECT estado, COUNT(*) as total FROM {$this->table} 
                WHERE status = 'ativo' AND estado IS NOT NULL 
                GROUP BY estado 
                ORDER BY total DESC 
                LIMIT 10";
        $stats['por_estado'] = $this->query($sql);
        
        // Últimos cadastros
        $sql = "SELECT nome, data_cadastro FROM {$this->table} 
                WHERE status = 'ativo' 
                ORDER BY data_cadastro DESC 
                LIMIT 5";
        $stats['ultimos_cadastros'] = $this->query($sql);
        
        return $stats;
    }
    
    /**
     * Buscar clientes com aniversário no mês
     */
    public function aniversariantesDoMes($mes = null) {
        $mes = $mes ?: date('m');
        
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'ativo' 
                AND MONTH(data_nascimento) = ? 
                ORDER BY DAY(data_nascimento) ASC";
        
        return $this->query($sql, [$mes]);
    }
    
    /**
     * Buscar histórico de agendamentos do cliente
     */
    public function getHistoricoAgendamentos($clienteId) {
        $sql = "SELECT a.*, p.nome as produto_nome, f.nome as funcionario_nome 
                FROM agendamentos a 
                LEFT JOIN produtos p ON a.produto_id = p.id 
                LEFT JOIN funcionarios f ON a.funcionario_id = f.id 
                WHERE a.cliente_id = ? 
                ORDER BY a.data_visita DESC, a.hora_inicio DESC";
        
        return $this->query($sql, [$clienteId]);
    }
}
