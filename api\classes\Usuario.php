<?php
/**
 * Modelo de Usuários
 * Sistema Saúde Flex - Agendamentos
 */

require_once __DIR__ . '/BaseModel.php';

class Usuario extends BaseModel {
    protected $table = 'usuarios';
    
    /**
     * Buscar usuário por email
     */
    public function findByEmail($email) {
        return $this->findOne(['email' => $email, 'status' => 'ativo']);
    }
    
    /**
     * Criar novo usuário
     */
    public function create($data) {
        // Criptografar senha
        if (isset($data['senha'])) {
            $data['senha'] = hashPassword($data['senha']);
        }
        
        $id = parent::create($data);
        
        if ($id) {
            $this->logAuditoria('CREATE', $id, null, $data);
        }
        
        return $id;
    }
    
    /**
     * Atualizar usuário
     */
    public function update($id, $data) {
        $dadosAnteriores = $this->findById($id);
        
        // Criptografar senha se fornecida
        if (isset($data['senha']) && !empty($data['senha'])) {
            $data['senha'] = hashPassword($data['senha']);
        } else {
            // Remover senha do array se estiver vazia
            unset($data['senha']);
        }
        
        $result = parent::update($id, $data);
        
        if ($result) {
            $this->logAuditoria('UPDATE', $id, $dadosAnteriores, $data);
        }
        
        return $result;
    }
    
    /**
     * Excluir usuário
     */
    public function delete($id) {
        $dadosAnteriores = $this->findById($id);
        $result = parent::delete($id);
        
        if ($result) {
            $this->logAuditoria('DELETE', $id, $dadosAnteriores);
        }
        
        return $result;
    }
    
    /**
     * Verificar credenciais de login
     */
    public function verificarCredenciais($email, $senha) {
        $usuario = $this->findByEmail($email);
        
        if ($usuario && verifyPassword($senha, $usuario['senha'])) {
            // Atualizar último login
            $this->update($usuario['id'], ['ultimo_login' => date('Y-m-d H:i:s')]);
            
            // Remover senha do retorno
            unset($usuario['senha']);
            
            return $usuario;
        }
        
        return false;
    }
    
    /**
     * Buscar usuários ativos
     */
    public function findAtivos() {
        return $this->findAll(['status' => 'ativo'], 'nome ASC');
    }
    
    /**
     * Buscar vendedores ativos
     */
    public function findVendedores() {
        $sql = "SELECT * FROM {$this->table} WHERE status = 'ativo' AND tipo IN ('vendedor', 'gerente', 'admin') ORDER BY nome ASC";
        return $this->query($sql);
    }
    
    /**
     * Verificar se email já existe
     */
    public function emailExists($email, $excludeId = null) {
        $conditions = ['email' => $email];
        
        if ($excludeId) {
            $sql = "SELECT COUNT(*) FROM {$this->table} WHERE email = ? AND id != ?";
            return $this->queryValue($sql, [$email, $excludeId]) > 0;
        }
        
        return $this->count($conditions) > 0;
    }
    
    /**
     * Validar dados do usuário
     */
    public function validar($data, $isUpdate = false) {
        $rules = [
            'nome' => ['required' => true, 'max_length' => 255],
            'email' => ['required' => true, 'type' => 'email', 'max_length' => 255],
            'tipo' => ['required' => true, 'in' => ['admin', 'gerente', 'vendedor']]
        ];
        
        if (!$isUpdate) {
            $rules['senha'] = ['required' => true, 'min_length' => 6];
        }
        
        $errors = validateInput($data, $rules);
        
        // Verificar se email já existe
        $excludeId = $isUpdate && isset($data['id']) ? $data['id'] : null;
        if (isset($data['email']) && $this->emailExists($data['email'], $excludeId)) {
            $errors['email'] = 'Este email já está em uso';
        }
        
        return $errors;
    }
    
    /**
     * Buscar estatísticas de usuários
     */
    public function getEstatisticas() {
        $stats = [];
        
        // Total de usuários ativos
        $stats['total_ativos'] = $this->count(['status' => 'ativo']);
        
        // Total por tipo
        $sql = "SELECT tipo, COUNT(*) as total FROM {$this->table} WHERE status = 'ativo' GROUP BY tipo";
        $tipos = $this->query($sql);
        
        foreach ($tipos as $tipo) {
            $stats['por_tipo'][$tipo['tipo']] = $tipo['total'];
        }
        
        // Últimos logins
        $sql = "SELECT nome, ultimo_login FROM {$this->table} WHERE status = 'ativo' AND ultimo_login IS NOT NULL ORDER BY ultimo_login DESC LIMIT 5";
        $stats['ultimos_logins'] = $this->query($sql);
        
        return $stats;
    }
}
