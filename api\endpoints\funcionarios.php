<?php
/**
 * Endpoints de Funcionários
 * Sistema Saúde Flex - Agendamentos
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/JWT.php';
require_once __DIR__ . '/../classes/Funcionario.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';
$id = $_GET['id'] ?? null;

$funcionario = new Funcionario();

switch ($method) {
    case 'GET':
        if ($id) {
            if ($path === 'agenda') {
                getAgenda($id);
            } else {
                getFuncionario($id);
            }
        } elseif ($path === 'search') {
            searchFuncionarios();
        } elseif ($path === 'especialidades') {
            getEspecialidades();
        } elseif ($path === 'disponiveis') {
            getDisponiveis();
        } elseif ($path === 'stats') {
            getEstatisticas();
        } else {
            getFuncionarios();
        }
        break;
        
    case 'POST':
        createFuncionario();
        break;
        
    case 'PUT':
        if ($id) {
            updateFuncionario($id);
        } else {
            jsonResponse(['error' => 'ID do funcionário é obrigatório'], 400);
        }
        break;
        
    case 'DELETE':
        if ($id) {
            deleteFuncionario($id);
        } else {
            jsonResponse(['error' => 'ID do funcionário é obrigatório'], 400);
        }
        break;
        
    default:
        jsonResponse(['error' => 'Método não permitido'], 405);
}

/**
 * Listar todos os funcionários
 */
function getFuncionarios() {
    global $funcionario;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $funcionarios = $funcionario->findWithFormattedData();
    
    jsonResponse(['funcionarios' => $funcionarios]);
}

/**
 * Buscar funcionário por ID
 */
function getFuncionario($id) {
    global $funcionario;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $funcionarioData = $funcionario->findById($id);
    
    if (!$funcionarioData) {
        jsonResponse(['error' => 'Funcionário não encontrado'], 404);
    }
    
    // Decodificar especialidades
    if ($funcionarioData['especialidades']) {
        $funcionarioData['especialidades'] = json_decode($funcionarioData['especialidades'], true);
    } else {
        $funcionarioData['especialidades'] = [];
    }
    
    jsonResponse($funcionarioData);
}

/**
 * Criar novo funcionário
 */
function createFuncionario() {
    global $funcionario;
    
    // Verificar autenticação e permissão
    JWT::checkPermission(['admin', 'gerente']);
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Dados inválidos'], 400);
    }
    
    // Sanitizar dados
    $data = sanitizeInput($input);
    
    // Validar dados
    $errors = $funcionario->validar($data);
    
    if (!empty($errors)) {
        jsonResponse(['error' => 'Dados inválidos', 'details' => $errors], 400);
    }
    
    $id = $funcionario->create($data);
    
    if ($id) {
        jsonResponse(['id' => $id, 'message' => 'Funcionário cadastrado com sucesso!'], 201);
    } else {
        jsonResponse(['error' => 'Erro ao cadastrar funcionário'], 500);
    }
}

/**
 * Atualizar funcionário
 */
function updateFuncionario($id) {
    global $funcionario;
    
    // Verificar autenticação e permissão
    JWT::checkPermission(['admin', 'gerente']);
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Dados inválidos'], 400);
    }
    
    // Verificar se funcionário existe
    if (!$funcionario->findById($id)) {
        jsonResponse(['error' => 'Funcionário não encontrado'], 404);
    }
    
    // Sanitizar dados
    $data = sanitizeInput($input);
    $data['id'] = $id; // Para validação de email único
    
    // Validar dados
    $errors = $funcionario->validar($data, true);
    
    if (!empty($errors)) {
        jsonResponse(['error' => 'Dados inválidos', 'details' => $errors], 400);
    }
    
    unset($data['id']); // Remover ID dos dados de atualização
    
    $result = $funcionario->update($id, $data);
    
    if ($result) {
        jsonResponse(['message' => 'Funcionário atualizado com sucesso!']);
    } else {
        jsonResponse(['error' => 'Erro ao atualizar funcionário'], 500);
    }
}

/**
 * Excluir funcionário
 */
function deleteFuncionario($id) {
    global $funcionario;
    
    // Verificar autenticação e permissão
    JWT::checkPermission(['admin', 'gerente']);
    
    // Verificar se funcionário existe
    if (!$funcionario->findById($id)) {
        jsonResponse(['error' => 'Funcionário não encontrado'], 404);
    }
    
    $result = $funcionario->delete($id);
    
    if ($result) {
        jsonResponse(['message' => 'Funcionário removido com sucesso!']);
    } else {
        jsonResponse(['error' => 'Erro ao remover funcionário'], 500);
    }
}

/**
 * Pesquisar funcionários
 */
function searchFuncionarios() {
    global $funcionario;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $termo = $_GET['q'] ?? '';
    
    if (empty($termo)) {
        jsonResponse(['error' => 'Termo de pesquisa é obrigatório'], 400);
    }
    
    $resultados = $funcionario->search($termo);
    
    // Formatar especialidades
    foreach ($resultados as &$func) {
        if ($func['especialidades']) {
            $func['especialidades'] = json_decode($func['especialidades'], true);
        } else {
            $func['especialidades'] = [];
        }
    }
    
    jsonResponse(['funcionarios' => $resultados]);
}

/**
 * Obter todas as especialidades
 */
function getEspecialidades() {
    global $funcionario;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $especialidades = $funcionario->getAllEspecialidades();
    
    jsonResponse(['especialidades' => $especialidades]);
}

/**
 * Obter funcionários disponíveis
 */
function getDisponiveis() {
    global $funcionario;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $data = $_GET['data'] ?? '';
    $horaInicio = $_GET['hora_inicio'] ?? '';
    $horaFim = $_GET['hora_fim'] ?? '';
    
    if (empty($data) || empty($horaInicio) || empty($horaFim)) {
        jsonResponse(['error' => 'Data, hora de início e hora de fim são obrigatórias'], 400);
    }
    
    $funcionarios = $funcionario->findDisponiveis($data, $horaInicio, $horaFim);
    
    // Formatar especialidades
    foreach ($funcionarios as &$func) {
        if ($func['especialidades']) {
            $func['especialidades'] = json_decode($func['especialidades'], true);
        } else {
            $func['especialidades'] = [];
        }
    }
    
    jsonResponse(['funcionarios' => $funcionarios]);
}

/**
 * Obter agenda do funcionário
 */
function getAgenda($id) {
    global $funcionario;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $dataInicio = $_GET['data_inicio'] ?? null;
    $dataFim = $_GET['data_fim'] ?? null;
    
    // Verificar se funcionário existe
    if (!$funcionario->findById($id)) {
        jsonResponse(['error' => 'Funcionário não encontrado'], 404);
    }
    
    $agenda = $funcionario->getAgenda($id, $dataInicio, $dataFim);
    
    jsonResponse(['agenda' => $agenda]);
}

/**
 * Obter estatísticas de funcionários
 */
function getEstatisticas() {
    global $funcionario;
    
    // Verificar autenticação e permissão
    JWT::checkPermission(['admin', 'gerente']);
    
    $stats = $funcionario->getEstatisticas();
    
    jsonResponse($stats);
}
