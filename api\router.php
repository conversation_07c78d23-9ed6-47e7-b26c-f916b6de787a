<?php
/**
 * Router da API PHP
 * Sistema Saúde Flex - Agendamentos
 * Migração Node.js → PHP + MariaDB
 */

// Incluir configurações
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

// Função para roteamento
function route($uri, $method) {
    // Remover query string
    $uri = strtok($uri, '?');
    
    // Remover /api/ do início se existir
    $uri = preg_replace('/^\/api/', '', $uri);
    
    // Dividir URI em partes
    $parts = explode('/', trim($uri, '/'));
    $endpoint = $parts[0] ?? '';
    $id = $parts[1] ?? null;
    $action = $parts[2] ?? null;
    
    // Mapear endpoints para arquivos
    $endpointMap = [
        'auth' => 'auth.php',
        'clientes' => 'clientes.php',
        'produtos' => 'produtos.php',
        'funcionarios' => 'funcionarios.php',
        'agendamentos' => 'agendamentos.php',
        'usuarios' => 'usuarios.php',
        'dashboard' => 'dashboard.php'
    ];
    
    // Verificar se endpoint existe
    if (!isset($endpointMap[$endpoint])) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'Endpoint não encontrado',
            'endpoint' => $endpoint
        ]);
        return;
    }
    
    // Definir variáveis para o endpoint
    $_GET['id'] = $id;
    $_GET['action'] = $action;
    $_SERVER['REQUEST_METHOD'] = $method;
    
    // Incluir arquivo do endpoint
    $endpointFile = __DIR__ . '/endpoints/' . $endpointMap[$endpoint];
    
    if (file_exists($endpointFile)) {
        include $endpointFile;
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Arquivo do endpoint não encontrado',
            'file' => $endpointFile
        ]);
    }
}

// Obter método e URI
$method = $_SERVER['REQUEST_METHOD'];
$uri = $_SERVER['REQUEST_URI'];

// Log da requisição
error_log("API Request: $method $uri");

try {
    // Processar rota
    route($uri, $method);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Erro interno do servidor',
        'message' => $e->getMessage()
    ]);
    error_log("API Error: " . $e->getMessage());
}
?>
