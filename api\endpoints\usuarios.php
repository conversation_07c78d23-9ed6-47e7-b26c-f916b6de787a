<?php
/**
 * Endpoints de Usuários
 * Sistema Saúde Flex - Agendamentos
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/JWT.php';
require_once __DIR__ . '/../classes/Usuario.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';
$id = $_GET['id'] ?? null;

$usuario = new Usuario();

switch ($method) {
    case 'GET':
        if ($id) {
            getUsuario($id);
        } elseif ($path === 'vendedores') {
            getVendedores();
        } elseif ($path === 'stats') {
            getEstatisticas();
        } else {
            getUsuarios();
        }
        break;
        
    case 'POST':
        createUsuario();
        break;
        
    case 'PUT':
        if ($id) {
            updateUsuario($id);
        } else {
            jsonResponse(['error' => 'ID do usuário é obrigatório'], 400);
        }
        break;
        
    case 'DELETE':
        if ($id) {
            deleteUsuario($id);
        } else {
            jsonResponse(['error' => 'ID do usuário é obrigatório'], 400);
        }
        break;
        
    default:
        jsonResponse(['error' => 'Método não permitido'], 405);
}

/**
 * Listar todos os usuários (apenas admins)
 */
function getUsuarios() {
    global $usuario;
    
    // Verificar autenticação e permissão
    JWT::checkPermission(['admin']);
    
    $usuarios = $usuario->findAll([], 'nome ASC');
    
    // Remover senhas
    foreach ($usuarios as &$user) {
        unset($user['senha']);
    }
    
    jsonResponse(['usuarios' => $usuarios]);
}

/**
 * Buscar usuário por ID
 */
function getUsuario($id) {
    global $usuario;
    
    // Verificar autenticação e permissão
    JWT::checkPermission(['admin']);
    
    $userData = $usuario->findById($id);
    
    if (!$userData) {
        jsonResponse(['error' => 'Usuário não encontrado'], 404);
    }
    
    // Remover senha
    unset($userData['senha']);
    
    jsonResponse($userData);
}

/**
 * Buscar vendedores ativos
 */
function getVendedores() {
    global $usuario;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $vendedores = $usuario->findVendedores();
    
    // Remover senhas
    foreach ($vendedores as &$vendedor) {
        unset($vendedor['senha']);
    }
    
    jsonResponse(['vendedores' => $vendedores]);
}

/**
 * Criar novo usuário (apenas admins)
 */
function createUsuario() {
    global $usuario;
    
    // Verificar autenticação e permissão
    JWT::checkPermission(['admin']);
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Dados inválidos'], 400);
    }
    
    // Sanitizar dados
    $data = sanitizeInput($input);
    
    // Validar dados
    $errors = $usuario->validar($data);
    
    if (!empty($errors)) {
        jsonResponse(['error' => 'Dados inválidos', 'details' => $errors], 400);
    }
    
    $id = $usuario->create($data);
    
    if ($id) {
        jsonResponse(['id' => $id, 'message' => 'Usuário criado com sucesso!'], 201);
    } else {
        jsonResponse(['error' => 'Erro ao criar usuário'], 500);
    }
}

/**
 * Atualizar usuário (apenas admins)
 */
function updateUsuario($id) {
    global $usuario;
    
    // Verificar autenticação e permissão
    $currentUser = JWT::checkPermission(['admin']);
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Dados inválidos'], 400);
    }
    
    // Verificar se usuário existe
    if (!$usuario->findById($id)) {
        jsonResponse(['error' => 'Usuário não encontrado'], 404);
    }
    
    // Não permitir que o usuário exclua a si mesmo
    if ($currentUser['id'] == $id && isset($input['status']) && $input['status'] === 'inativo') {
        jsonResponse(['error' => 'Não é possível desativar seu próprio usuário'], 400);
    }
    
    // Sanitizar dados
    $data = sanitizeInput($input);
    $data['id'] = $id; // Para validação de email único
    
    // Validar dados
    $errors = $usuario->validar($data, true);
    
    if (!empty($errors)) {
        jsonResponse(['error' => 'Dados inválidos', 'details' => $errors], 400);
    }
    
    unset($data['id']); // Remover ID dos dados de atualização
    
    $result = $usuario->update($id, $data);
    
    if ($result) {
        jsonResponse(['message' => 'Usuário atualizado com sucesso!']);
    } else {
        jsonResponse(['error' => 'Erro ao atualizar usuário'], 500);
    }
}

/**
 * Excluir usuário (apenas admins)
 */
function deleteUsuario($id) {
    global $usuario;
    
    // Verificar autenticação e permissão
    $currentUser = JWT::checkPermission(['admin']);
    
    // Verificar se usuário existe
    if (!$usuario->findById($id)) {
        jsonResponse(['error' => 'Usuário não encontrado'], 404);
    }
    
    // Não permitir que o usuário exclua a si mesmo
    if ($currentUser['id'] == $id) {
        jsonResponse(['error' => 'Não é possível excluir seu próprio usuário'], 400);
    }
    
    $result = $usuario->delete($id);
    
    if ($result) {
        jsonResponse(['message' => 'Usuário excluído com sucesso!']);
    } else {
        jsonResponse(['error' => 'Erro ao excluir usuário'], 500);
    }
}

/**
 * Obter estatísticas de usuários
 */
function getEstatisticas() {
    global $usuario;
    
    // Verificar autenticação e permissão
    JWT::checkPermission(['admin']);
    
    $stats = $usuario->getEstatisticas();
    
    jsonResponse($stats);
}
