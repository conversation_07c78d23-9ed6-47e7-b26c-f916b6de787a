<?php
/**
 * Configurações Gerais do Sistema
 * Sistema Saúde Flex - Agendamentos
 */

// Configurações de CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// Responder a requisições OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configurações do JWT
define('JWT_SECRET', 'saude_flex_secret_key_2024_php');
define('JWT_ALGORITHM', 'HS256');
define('JWT_EXPIRATION', 86400); // 24 horas

// Configurações do sistema
define('SYSTEM_NAME', 'Saúde Flex');
define('SYSTEM_VERSION', '1.0.0');
define('TIMEZONE', 'America/Sao_Paulo');

// Configurar timezone
date_default_timezone_set(TIMEZONE);

// Configurações de erro
error_reporting(E_ALL);
ini_set('display_errors', 0); // Não mostrar erros em produção

// Função para resposta JSON padronizada
function jsonResponse($data, $status = 200, $message = null) {
    http_response_code($status);
    
    $response = [];
    
    if ($message) {
        $response['message'] = $message;
    }
    
    if ($status >= 400) {
        $response['error'] = $data;
    } else {
        $response = array_merge($response, $data);
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit();
}

// Função para log de erros
function logError($message, $context = []) {
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => $message,
        'context' => $context
    ];
    
    error_log(json_encode($logData, JSON_UNESCAPED_UNICODE));
}

// Função para validar dados de entrada
function validateInput($data, $rules) {
    $errors = [];
    
    foreach ($rules as $field => $rule) {
        $value = $data[$field] ?? null;
        
        // Campo obrigatório
        if (isset($rule['required']) && $rule['required'] && empty($value)) {
            $errors[$field] = "Campo {$field} é obrigatório";
            continue;
        }
        
        // Se campo não é obrigatório e está vazio, pular outras validações
        if (empty($value) && (!isset($rule['required']) || !$rule['required'])) {
            continue;
        }
        
        // Validação de tipo
        if (isset($rule['type'])) {
            switch ($rule['type']) {
                case 'email':
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[$field] = "Campo {$field} deve ser um email válido";
                    }
                    break;
                    
                case 'numeric':
                    if (!is_numeric($value)) {
                        $errors[$field] = "Campo {$field} deve ser numérico";
                    }
                    break;
                    
                case 'date':
                    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
                        $errors[$field] = "Campo {$field} deve estar no formato YYYY-MM-DD";
                    }
                    break;
                    
                case 'time':
                    if (!preg_match('/^\d{2}:\d{2}$/', $value)) {
                        $errors[$field] = "Campo {$field} deve estar no formato HH:MM";
                    }
                    break;
            }
        }
        
        // Validação de tamanho mínimo
        if (isset($rule['min_length']) && strlen($value) < $rule['min_length']) {
            $errors[$field] = "Campo {$field} deve ter pelo menos {$rule['min_length']} caracteres";
        }
        
        // Validação de tamanho máximo
        if (isset($rule['max_length']) && strlen($value) > $rule['max_length']) {
            $errors[$field] = "Campo {$field} deve ter no máximo {$rule['max_length']} caracteres";
        }
        
        // Validação de valores permitidos
        if (isset($rule['in']) && !in_array($value, $rule['in'])) {
            $errors[$field] = "Campo {$field} deve ser um dos valores: " . implode(', ', $rule['in']);
        }
    }
    
    return $errors;
}

// Função para sanitizar dados de entrada
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

// Função para gerar hash de senha
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// Função para verificar senha
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Função para gerar UUID simples
function generateUuid() {
    return sprintf(
        '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}

// Função para formatar valor monetário
function formatMoney($value) {
    return 'R$ ' . number_format($value, 2, ',', '.');
}

// Função para converter data para formato brasileiro
function formatDateBR($date) {
    return date('d/m/Y', strtotime($date));
}

// Função para converter data e hora para formato brasileiro
function formatDateTimeBR($datetime) {
    return date('d/m/Y H:i', strtotime($datetime));
}
