<?php
/**
 * Modelo de Produtos
 * Sistema Saúde Flex - Agendamentos
 */

require_once __DIR__ . '/BaseModel.php';

class Produto extends BaseModel {
    protected $table = 'produtos';
    
    /**
     * Buscar produtos ativos
     */
    public function findAtivos() {
        return $this->findAll(['status' => 'ativo'], 'categoria ASC, nome ASC');
    }
    
    /**
     * Criar novo produto
     */
    public function create($data) {
        // Calcular preço de venda se não fornecido
        if (!isset($data['preco_venda']) || empty($data['preco_venda'])) {
            $data['preco_venda'] = $this->calcularPrecoVenda($data);
        }
        
        $id = parent::create($data);
        
        if ($id) {
            $this->logAuditoria('CREATE', $id, null, $data);
        }
        
        return $id;
    }
    
    /**
     * Atualizar produto
     */
    public function update($id, $data) {
        $dadosAnteriores = $this->findById($id);
        
        // Recalcular preço de venda se necessário
        if (isset($data['preco_custo']) || isset($data['lucro_desejado']) || isset($data['tipo_lucro'])) {
            $produtoAtual = $this->findById($id);
            $dadosCompletos = array_merge($produtoAtual, $data);
            $data['preco_venda'] = $this->calcularPrecoVenda($dadosCompletos);
        }
        
        $result = parent::update($id, $data);
        
        if ($result) {
            $this->logAuditoria('UPDATE', $id, $dadosAnteriores, $data);
        }
        
        return $result;
    }
    
    /**
     * Excluir produto (soft delete)
     */
    public function delete($id) {
        $dadosAnteriores = $this->findById($id);
        $result = $this->softDelete($id);
        
        if ($result) {
            $this->logAuditoria('DELETE', $id, $dadosAnteriores);
        }
        
        return $result;
    }
    
    /**
     * Calcular preço de venda
     */
    private function calcularPrecoVenda($data) {
        $precoCusto = floatval($data['preco_custo'] ?? 0);
        $lucroDesejado = floatval($data['lucro_desejado'] ?? 0);
        $tipoLucro = $data['tipo_lucro'] ?? 'percentual';
        
        if ($tipoLucro === 'percentual') {
            return $precoCusto + ($precoCusto * ($lucroDesejado / 100));
        } else {
            return $precoCusto + $lucroDesejado;
        }
    }
    
    /**
     * Buscar produtos por categoria
     */
    public function findByCategoria($categoria) {
        return $this->findAll(['categoria' => $categoria, 'status' => 'ativo'], 'nome ASC');
    }
    
    /**
     * Buscar produtos por termo de pesquisa
     */
    public function search($termo) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'ativo' 
                AND (nome LIKE ? OR descricao LIKE ? OR categoria LIKE ?) 
                ORDER BY nome ASC";
        
        $termoBusca = "%{$termo}%";
        return $this->query($sql, [$termoBusca, $termoBusca, $termoBusca]);
    }
    
    /**
     * Buscar produtos com estoque baixo
     */
    public function findEstoqueBaixo() {
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'ativo' 
                AND estoque_atual <= estoque_minimo 
                ORDER BY estoque_atual ASC";
        
        return $this->query($sql);
    }
    
    /**
     * Buscar categorias disponíveis
     */
    public function getCategorias() {
        $sql = "SELECT DISTINCT categoria FROM {$this->table} 
                WHERE status = 'ativo' AND categoria IS NOT NULL 
                ORDER BY categoria ASC";
        
        $result = $this->query($sql);
        return array_column($result, 'categoria');
    }
    
    /**
     * Atualizar estoque
     */
    public function atualizarEstoque($id, $quantidade, $operacao = 'subtrair') {
        $produto = $this->findById($id);
        
        if (!$produto) {
            return false;
        }
        
        $novoEstoque = $produto['estoque_atual'];
        
        if ($operacao === 'subtrair') {
            $novoEstoque -= $quantidade;
        } else {
            $novoEstoque += $quantidade;
        }
        
        // Não permitir estoque negativo
        if ($novoEstoque < 0) {
            $novoEstoque = 0;
        }
        
        return $this->update($id, ['estoque_atual' => $novoEstoque]);
    }
    
    /**
     * Validar dados do produto
     */
    public function validar($data, $isUpdate = false) {
        $rules = [
            'nome' => ['required' => true, 'max_length' => 255],
            'categoria' => ['max_length' => 100],
            'preco_custo' => ['type' => 'numeric'],
            'lucro_desejado' => ['type' => 'numeric'],
            'tipo_lucro' => ['in' => ['valor', 'percentual']],
            'preco_venda' => ['type' => 'numeric'],
            'estoque_atual' => ['type' => 'numeric'],
            'estoque_minimo' => ['type' => 'numeric'],
            'duracao_estimada' => ['type' => 'numeric']
        ];
        
        $errors = validateInput($data, $rules);
        
        // Validações específicas
        if (isset($data['preco_custo']) && $data['preco_custo'] < 0) {
            $errors['preco_custo'] = 'Preço de custo não pode ser negativo';
        }
        
        if (isset($data['lucro_desejado']) && $data['lucro_desejado'] < 0) {
            $errors['lucro_desejado'] = 'Lucro desejado não pode ser negativo';
        }
        
        if (isset($data['estoque_atual']) && $data['estoque_atual'] < 0) {
            $errors['estoque_atual'] = 'Estoque atual não pode ser negativo';
        }
        
        if (isset($data['estoque_minimo']) && $data['estoque_minimo'] < 0) {
            $errors['estoque_minimo'] = 'Estoque mínimo não pode ser negativo';
        }
        
        return $errors;
    }
    
    /**
     * Buscar estatísticas de produtos
     */
    public function getEstatisticas() {
        $stats = [];
        
        // Total de produtos ativos
        $stats['total_ativos'] = $this->count(['status' => 'ativo']);
        
        // Produtos com estoque baixo
        $stats['estoque_baixo'] = count($this->findEstoqueBaixo());
        
        // Distribuição por categoria
        $sql = "SELECT categoria, COUNT(*) as total FROM {$this->table} 
                WHERE status = 'ativo' AND categoria IS NOT NULL 
                GROUP BY categoria 
                ORDER BY total DESC";
        $stats['por_categoria'] = $this->query($sql);
        
        // Valor total do estoque
        $sql = "SELECT SUM(preco_custo * estoque_atual) as valor_estoque FROM {$this->table} 
                WHERE status = 'ativo'";
        $stats['valor_estoque'] = $this->queryValue($sql) ?: 0;
        
        // Produtos mais caros
        $sql = "SELECT nome, preco_venda FROM {$this->table} 
                WHERE status = 'ativo' 
                ORDER BY preco_venda DESC 
                LIMIT 5";
        $stats['mais_caros'] = $this->query($sql);
        
        return $stats;
    }
    
    /**
     * Buscar produtos mais vendidos (requer tabela de vendas)
     */
    public function getMaisVendidos($limite = 10) {
        $sql = "SELECT p.*, COALESCE(SUM(iv.quantidade), 0) as total_vendido 
                FROM {$this->table} p 
                LEFT JOIN itens_venda iv ON p.id = iv.produto_id 
                WHERE p.status = 'ativo' 
                GROUP BY p.id 
                ORDER BY total_vendido DESC 
                LIMIT ?";
        
        return $this->query($sql, [$limite]);
    }
}
