<?php
/**
 * Classe JWT para autenticação
 * Sistema Saúde Flex - Agendamentos
 */

class JWT {
    
    /**
     * Gerar token JWT
     */
    public static function encode($payload, $secret = null) {
        $secret = $secret ?: JWT_SECRET;
        
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode($payload);
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $secret, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }
    
    /**
     * Decodificar token JWT
     */
    public static function decode($jwt, $secret = null) {
        $secret = $secret ?: JWT_SECRET;
        
        $tokenParts = explode('.', $jwt);
        
        if (count($tokenParts) !== 3) {
            return false;
        }
        
        $header = base64_decode(str_replace(['-', '_'], ['+', '/'], $tokenParts[0]));
        $payload = base64_decode(str_replace(['-', '_'], ['+', '/'], $tokenParts[1]));
        $signatureProvided = $tokenParts[2];
        
        // Verificar se o token expirou
        $payloadArray = json_decode($payload, true);
        if (isset($payloadArray['exp']) && $payloadArray['exp'] < time()) {
            return false;
        }
        
        // Verificar assinatura
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $secret, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        if ($base64Signature !== $signatureProvided) {
            return false;
        }
        
        return json_decode($payload, true);
    }
    
    /**
     * Verificar se token é válido
     */
    public static function verify($jwt, $secret = null) {
        return self::decode($jwt, $secret) !== false;
    }
    
    /**
     * Extrair token do header Authorization
     */
    public static function getBearerToken() {
        $headers = getallheaders();
        
        if (isset($headers['Authorization'])) {
            $authHeader = $headers['Authorization'];
            if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
                return $matches[1];
            }
        }
        
        return null;
    }
    
    /**
     * Middleware para verificar autenticação
     */
    public static function authenticate() {
        $token = self::getBearerToken();
        
        if (!$token) {
            jsonResponse(['error' => 'Token de acesso requerido'], 401);
        }
        
        $payload = self::decode($token);
        
        if (!$payload) {
            jsonResponse(['error' => 'Token inválido ou expirado'], 403);
        }
        
        return $payload;
    }
    
    /**
     * Middleware para verificar permissões
     */
    public static function checkPermission($requiredTypes) {
        $user = self::authenticate();
        
        if (!in_array($user['tipo'], $requiredTypes)) {
            jsonResponse(['error' => 'Permissão insuficiente'], 403);
        }
        
        return $user;
    }
}
