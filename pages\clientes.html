<!-- Página de Clientes -->
<div class="page-header">
    <div>
        <h1 class="page-title">Clientes</h1>
        <p class="page-subtitle">Gerencie sua base de clientes</p>
    </div>
    <div style="display: flex; gap: 1rem;">
        <button class="btn btn-secondary" onclick="exportarClientes()">
            <i class="fas fa-download"></i>
            Exportar
        </button>
        <button class="btn btn-primary" onclick="openModal('novoClienteModal')">
            <i class="fas fa-plus"></i>
            Novo Cliente
        </button>
    </div>
</div>

<!-- Estatísticas Rápidas -->
<div class="stats-grid" style="margin-bottom: 2rem;">
    <div class="stat-card">
        <div class="stat-value" id="totalClientesAtivos">0</div>
        <div class="stat-label">Clientes Ativos</div>
    </div>
    <div class="stat-card">
        <div class="stat-value" id="novosClientesMes">0</div>
        <div class="stat-label">Novos Este Mês</div>
    </div>
    <div class="stat-card">
        <div class="stat-value" id="clientesComAgendamentos">0</div>
        <div class="stat-label">Com Agendamentos</div>
    </div>
    <div class="stat-card">
        <div class="stat-value" id="ticketMedioClientes">R$ 0</div>
        <div class="stat-label">Ticket Médio</div>
    </div>
</div>

<!-- Filtros -->
<div class="filters-container">
    <div class="filters-row">
        <div class="filter-group">
            <label class="filter-label">Buscar</label>
            <input type="text" class="filter-input" id="filtroBuscaClientes" placeholder="Nome, email, telefone...">
        </div>
        <div class="filter-group">
            <label class="filter-label">Cidade</label>
            <select class="filter-input" id="filtroCidade">
                <option value="">Todas as cidades</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Status</label>
            <select class="filter-input" id="filtroStatusCliente">
                <option value="">Todos os status</option>
                <option value="ativo">Ativo</option>
                <option value="inativo">Inativo</option>
            </select>
        </div>
        <div class="filter-group">
            <button class="btn btn-primary" onclick="aplicarFiltrosClientes()">
                <i class="fas fa-search"></i>
                Filtrar
            </button>
            <button class="btn btn-secondary" onclick="limparFiltrosClientes()">
                <i class="fas fa-times"></i>
                Limpar
            </button>
        </div>
    </div>
</div>

<!-- Lista de Clientes -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Lista de Clientes</h3>
        <div style="display: flex; gap: 1rem; align-items: center;">
            <span id="contadorClientes" style="color: var(--gray-600); font-size: 0.875rem;">0 clientes</span>
            <div class="dropdown">
                <button class="btn btn-sm btn-secondary dropdown-toggle">
                    <i class="fas fa-sort"></i> Ordenar
                </button>
                <div class="dropdown-menu">
                    <div class="dropdown-item" onclick="ordenarClientesPor('nome')">Nome</div>
                    <div class="dropdown-item" onclick="ordenarClientesPor('email')">Email</div>
                    <div class="dropdown-item" onclick="ordenarClientesPor('cidade')">Cidade</div>
                    <div class="dropdown-item" onclick="ordenarClientesPor('data_cadastro')">Data de Cadastro</div>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>Nome</th>
                        <th>Email</th>
                        <th>Telefone</th>
                        <th>Cidade</th>
                        <th>Data de Nascimento</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody id="clientesTableBody">
                    <!-- Dados serão carregados dinamicamente -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Novo Cliente -->
<div id="novoClienteModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">Novo Cliente</h2>
            <button class="modal-close" onclick="closeModal('novoClienteModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <form id="novoClienteForm">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Nome Completo *</label>
                        <input type="text" class="form-control" name="nome" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Email</label>
                        <input type="email" class="form-control" name="email">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Telefone</label>
                        <input type="tel" class="form-control" name="telefone" placeholder="(11) 99999-9999">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Data de Nascimento</label>
                        <input type="date" class="form-control" name="data_nascimento">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Gênero</label>
                        <select class="form-control" name="genero">
                            <option value="">Selecione</option>
                            <option value="masculino">Masculino</option>
                            <option value="feminino">Feminino</option>
                            <option value="outro">Outro</option>
                            <option value="nao_informar">Prefiro não informar</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">CEP</label>
                        <input type="text" class="form-control" name="cep" placeholder="00000-000">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Endereço</label>
                    <input type="text" class="form-control" name="endereco" placeholder="Rua, número, complemento">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Cidade</label>
                        <input type="text" class="form-control" name="cidade">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Estado</label>
                        <select class="form-control" name="estado">
                            <option value="">Selecione</option>
                            <option value="AC">Acre</option>
                            <option value="AL">Alagoas</option>
                            <option value="AP">Amapá</option>
                            <option value="AM">Amazonas</option>
                            <option value="BA">Bahia</option>
                            <option value="CE">Ceará</option>
                            <option value="DF">Distrito Federal</option>
                            <option value="ES">Espírito Santo</option>
                            <option value="GO">Goiás</option>
                            <option value="MA">Maranhão</option>
                            <option value="MT">Mato Grosso</option>
                            <option value="MS">Mato Grosso do Sul</option>
                            <option value="MG">Minas Gerais</option>
                            <option value="PA">Pará</option>
                            <option value="PB">Paraíba</option>
                            <option value="PR">Paraná</option>
                            <option value="PE">Pernambuco</option>
                            <option value="PI">Piauí</option>
                            <option value="RJ">Rio de Janeiro</option>
                            <option value="RN">Rio Grande do Norte</option>
                            <option value="RS">Rio Grande do Sul</option>
                            <option value="RO">Rondônia</option>
                            <option value="RR">Roraima</option>
                            <option value="SC">Santa Catarina</option>
                            <option value="SP">São Paulo</option>
                            <option value="SE">Sergipe</option>
                            <option value="TO">Tocantins</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Observações</label>
                    <textarea class="form-control" name="observacoes" rows="3" placeholder="Informações adicionais sobre o cliente"></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('novoClienteModal')">
                Cancelar
            </button>
            <button type="submit" form="novoClienteForm" class="btn btn-primary">
                <i class="fas fa-save"></i>
                Salvar Cliente
            </button>
        </div>
    </div>
</div>

<script>
// Variáveis globais da página de clientes
let clientesFiltrados = [];
let ordenacaoClientesAtual = { campo: 'nome', direcao: 'asc' };

// Carregar lista de clientes
function carregarListaClientes() {
    clientesFiltrados = [...app.data.clientes];
    aplicarOrdenacaoClientes();
    renderizarTabelaClientes();
    atualizarEstatisticasClientes();
    popularFiltrosCidades();
}

// Renderizar tabela de clientes
function renderizarTabelaClientes() {
    const tbody = document.getElementById('clientesTableBody');
    if (!tbody) return;

    if (clientesFiltrados.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center" style="padding: 2rem; color: var(--gray-500);">
                    Nenhum cliente encontrado
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = clientesFiltrados.map(cliente => `
        <tr>
            <td style="font-weight: 600;">${cliente.nome}</td>
            <td>${cliente.email || '-'}</td>
            <td>${formatters.phone(cliente.telefone) || '-'}</td>
            <td>${cliente.cidade || '-'}</td>
            <td>${cliente.data_nascimento ? formatters.date(cliente.data_nascimento) : '-'}</td>
            <td>
                <span class="badge badge-${cliente.status === 'ativo' ? 'success' : 'error'}">
                    ${cliente.status === 'ativo' ? 'Ativo' : 'Inativo'}
                </span>
            </td>
            <td>
                <div class="dropdown">
                    <button class="btn btn-sm btn-secondary dropdown-toggle">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="dropdown-menu">
                        <div class="dropdown-item" onclick="editarCliente(${cliente.id})">
                            <i class="fas fa-edit"></i> Editar
                        </div>
                        <div class="dropdown-item" onclick="verHistoricoCliente(${cliente.id})">
                            <i class="fas fa-history"></i> Histórico
                        </div>
                        <div class="dropdown-item" onclick="novoAgendamentoParaCliente(${cliente.id})">
                            <i class="fas fa-calendar-plus"></i> Agendar Visita
                        </div>
                        <div class="dropdown-item" onclick="alterarStatusCliente(${cliente.id}, '${cliente.status === 'ativo' ? 'inativo' : 'ativo'}')">
                            <i class="fas fa-toggle-${cliente.status === 'ativo' ? 'off' : 'on'}"></i> 
                            ${cliente.status === 'ativo' ? 'Desativar' : 'Ativar'}
                        </div>
                    </div>
                </div>
            </td>
        </tr>
    `).join('');

    // Configurar dropdowns
    configurarDropdowns();

    // Atualizar contador
    const contador = document.getElementById('contadorClientes');
    if (contador) {
        contador.textContent = `${clientesFiltrados.length} cliente${clientesFiltrados.length !== 1 ? 's' : ''}`;
    }
}

// Aplicar filtros de clientes
function aplicarFiltrosClientes() {
    const busca = document.getElementById('filtroBuscaClientes').value.toLowerCase();
    const cidade = document.getElementById('filtroCidade').value;
    const status = document.getElementById('filtroStatusCliente').value;

    clientesFiltrados = app.data.clientes.filter(cliente => {
        // Filtro de busca
        if (busca && !cliente.nome.toLowerCase().includes(busca) &&
            !(cliente.email && cliente.email.toLowerCase().includes(busca)) &&
            !(cliente.telefone && cliente.telefone.includes(busca))) {
            return false;
        }

        // Filtro de cidade
        if (cidade && cliente.cidade !== cidade) return false;

        // Filtro de status
        if (status && cliente.status !== status) return false;

        return true;
    });

    aplicarOrdenacaoClientes();
    renderizarTabelaClientes();
    atualizarEstatisticasClientes();
}

// Limpar filtros
function limparFiltrosClientes() {
    document.getElementById('filtroBuscaClientes').value = '';
    document.getElementById('filtroCidade').value = '';
    document.getElementById('filtroStatusCliente').value = '';
    carregarListaClientes();
}

// Aplicar ordenação
function aplicarOrdenacaoClientes() {
    clientesFiltrados.sort((a, b) => {
        let valorA = a[ordenacaoClientesAtual.campo] || '';
        let valorB = b[ordenacaoClientesAtual.campo] || '';

        if (typeof valorA === 'string') {
            valorA = valorA.toLowerCase();
            valorB = valorB.toLowerCase();
        }

        if (ordenacaoClientesAtual.direcao === 'asc') {
            return valorA > valorB ? 1 : -1;
        } else {
            return valorA < valorB ? 1 : -1;
        }
    });
}

// Ordenar por campo
function ordenarClientesPor(campo) {
    if (ordenacaoClientesAtual.campo === campo) {
        ordenacaoClientesAtual.direcao = ordenacaoClientesAtual.direcao === 'asc' ? 'desc' : 'asc';
    } else {
        ordenacaoClientesAtual.campo = campo;
        ordenacaoClientesAtual.direcao = 'asc';
    }

    aplicarOrdenacaoClientes();
    renderizarTabelaClientes();
}

// Popular filtro de cidades
function popularFiltrosCidades() {
    const cidades = [...new Set(app.data.clientes.map(c => c.cidade).filter(c => c))].sort();
    const select = document.getElementById('filtroCidade');
    if (select) {
        select.innerHTML = '<option value="">Todas as cidades</option>';
        cidades.forEach(cidade => {
            select.innerHTML += `<option value="${cidade}">${cidade}</option>`;
        });
    }
}

// Atualizar estatísticas
function atualizarEstatisticasClientes() {
    const totalAtivos = app.data.clientes.filter(c => c.status === 'ativo').length;
    const hoje = new Date();
    const inicioMes = new Date(hoje.getFullYear(), hoje.getMonth(), 1);
    const novosEsteMes = app.data.clientes.filter(c => new Date(c.data_cadastro) >= inicioMes).length;
    
    document.getElementById('totalClientesAtivos').textContent = totalAtivos;
    document.getElementById('novosClientesMes').textContent = novosEsteMes;
    document.getElementById('clientesComAgendamentos').textContent = '0'; // TODO: calcular
    document.getElementById('ticketMedioClientes').textContent = 'R$ 0'; // TODO: calcular
}

// Configurar formulário de cliente
document.getElementById('novoClienteForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    if (!validateForm(e.target)) {
        notifications.warning('Por favor, preencha todos os campos obrigatórios');
        return;
    }

    const formData = new FormData(e.target);
    const cliente = Object.fromEntries(formData.entries());
    const isEditing = e.target.dataset.editingId;

    try {
        let result;
        if (isEditing) {
            result = await api.updateCliente(isEditing, cliente);
            notifications.success('Cliente atualizado com sucesso!');
        } else {
            result = await api.createCliente(cliente);
            notifications.success('Cliente cadastrado com sucesso!');
        }

        // Atualizar dados locais
        await app.loadInitialData();
        carregarListaClientes();

        // Fechar modal
        closeModal('novoClienteModal');

        // Limpar formulário
        e.target.reset();
        delete e.target.dataset.editingId;

        // Restaurar título do modal
        const modalTitle = document.querySelector('#novoClienteModal .modal-title');
        if (modalTitle) modalTitle.textContent = 'Novo Cliente';

    } catch (error) {
        console.error('Erro ao salvar cliente:', error);
        notifications.error('Erro ao salvar cliente');
    }
});

// Funções auxiliares
function editarCliente(id) {
    const cliente = app.data.clientes.find(c => c.id === id);
    if (!cliente) return;

    const form = document.getElementById('novoClienteForm');
    Object.keys(cliente).forEach(key => {
        const input = form.querySelector(`[name="${key}"]`);
        if (input) {
            input.value = cliente[key] || '';
        }
    });

    form.dataset.editingId = id;
    const modalTitle = document.querySelector('#novoClienteModal .modal-title');
    if (modalTitle) modalTitle.textContent = 'Editar Cliente';

    openModal('novoClienteModal');
}

function verHistoricoCliente(id) {
    // TODO: Implementar modal de histórico
    notifications.info('Funcionalidade em desenvolvimento');
}

function novoAgendamentoParaCliente(id) {
    const form = document.getElementById('novoAgendamentoForm');
    if (form) {
        form.querySelector('[name="cliente_id"]').value = id;
        openModal('novoAgendamentoModal');
    }
}

function alterarStatusCliente(id, novoStatus) {
    if (confirm(`Tem certeza que deseja ${novoStatus === 'ativo' ? 'ativar' : 'desativar'} este cliente?`)) {
        fetch(`/api/clientes/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status: novoStatus })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                notifications.error(data.error);
            } else {
                notifications.success(`Cliente ${novoStatus === 'ativo' ? 'ativado' : 'desativado'} com sucesso!`);
                app.loadInitialData().then(() => {
                    carregarListaClientes();
                });
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            notifications.error('Erro ao alterar status do cliente');
        });
    }
}

function exportarClientes() {
    const dados = clientesFiltrados.map(cliente => ({
        'Nome': cliente.nome,
        'Email': cliente.email || '',
        'Telefone': cliente.telefone || '',
        'Cidade': cliente.cidade || '',
        'Estado': cliente.estado || '',
        'Data de Nascimento': cliente.data_nascimento ? formatters.date(cliente.data_nascimento) : '',
        'Gênero': cliente.genero || '',
        'Status': cliente.status,
        'Data de Cadastro': formatters.date(cliente.data_cadastro)
    }));

    // Converter para CSV
    const headers = Object.keys(dados[0] || {});
    const csvContent = [
        headers.join(','),
        ...dados.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');

    // Download do arquivo
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `clientes_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>
