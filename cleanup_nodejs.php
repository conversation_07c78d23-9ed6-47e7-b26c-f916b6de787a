<?php
/**
 * Script de Limpeza - Migração Node.js → PHP
 * Sistema Saúde Flex - Agendamentos
 */

echo "<h1>🧹 Limpeza de Arquivos Node.js</h1>";
echo "<p>Este script remove os arquivos do Node.js que não são mais necessários após a migração para PHP + MariaDB.</p>";

// Arquivos e diretórios para remover
$filesToRemove = [
    'server.js',
    'database.js', 
    'package.json',
    'package-lock.json',
    'saude_flex.db', // Banco SQLite antigo
    'database_setup.sql'
];

$directoriesToRemove = [
    'node_modules'
];

$removedFiles = [];
$removedDirs = [];
$errors = [];

echo "<h2>📁 Removendo Arquivos...</h2>";

// Remover arquivos
foreach ($filesToRemove as $file) {
    if (file_exists($file)) {
        if (unlink($file)) {
            $removedFiles[] = $file;
            echo "✅ Removido: <strong>$file</strong><br>";
        } else {
            $errors[] = "Erro ao remover arquivo: $file";
            echo "❌ Erro ao remover: <strong>$file</strong><br>";
        }
    } else {
        echo "⚠️ Arquivo não encontrado: <strong>$file</strong><br>";
    }
}

echo "<h2>📂 Removendo Diretórios...</h2>";

// Função para remover diretório recursivamente
function removeDirectory($dir) {
    if (!is_dir($dir)) {
        return false;
    }
    
    $files = array_diff(scandir($dir), array('.', '..'));
    
    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        if (is_dir($path)) {
            removeDirectory($path);
        } else {
            unlink($path);
        }
    }
    
    return rmdir($dir);
}

// Remover diretórios
foreach ($directoriesToRemove as $dir) {
    if (is_dir($dir)) {
        echo "🗂️ Removendo diretório: <strong>$dir</strong>...<br>";
        
        if (removeDirectory($dir)) {
            $removedDirs[] = $dir;
            echo "✅ Diretório removido: <strong>$dir</strong><br>";
        } else {
            $errors[] = "Erro ao remover diretório: $dir";
            echo "❌ Erro ao remover diretório: <strong>$dir</strong><br>";
        }
    } else {
        echo "⚠️ Diretório não encontrado: <strong>$dir</strong><br>";
    }
}

echo "<h2>📊 Resumo da Limpeza</h2>";

if (count($removedFiles) > 0) {
    echo "<h3>✅ Arquivos Removidos (" . count($removedFiles) . "):</h3>";
    echo "<ul>";
    foreach ($removedFiles as $file) {
        echo "<li>$file</li>";
    }
    echo "</ul>";
}

if (count($removedDirs) > 0) {
    echo "<h3>✅ Diretórios Removidos (" . count($removedDirs) . "):</h3>";
    echo "<ul>";
    foreach ($removedDirs as $dir) {
        echo "<li>$dir</li>";
    }
    echo "</ul>";
}

if (count($errors) > 0) {
    echo "<h3>❌ Erros Encontrados (" . count($errors) . "):</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>$error</li>";
    }
    echo "</ul>";
}

// Verificar espaço liberado
$totalRemoved = count($removedFiles) + count($removedDirs);

if ($totalRemoved > 0) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 Limpeza Concluída!</h3>";
    echo "<p><strong>$totalRemoved itens removidos com sucesso!</strong></p>";
    echo "<p>O sistema agora está executando 100% em PHP + MariaDB.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>⚠️ Nenhum Arquivo Removido</h3>";
    echo "<p>Os arquivos Node.js podem já ter sido removidos anteriormente.</p>";
    echo "</div>";
}

echo "<h2>🚀 Próximos Passos</h2>";
echo "<ol>";
echo "<li>✅ <strong>Migração Concluída:</strong> Sistema convertido para PHP + MariaDB</li>";
echo "<li>🔧 <strong>Teste o Sistema:</strong> <a href='test_connection.php'>Testar Conexão</a></li>";
echo "<li>🔐 <strong>Faça Login:</strong> <a href='login.html'>Acessar Sistema</a></li>";
echo "<li>📝 <strong>Credenciais:</strong> <EMAIL> / admin123</li>";
echo "<li>🗑️ <strong>Remover este arquivo:</strong> Você pode deletar cleanup_nodejs.php após a limpeza</li>";
echo "</ol>";

echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>📋 Estrutura Final do Sistema</h3>";
echo "<pre>";
echo "Saúde Flex/
├── api/                    # Backend PHP
│   ├── config/            # Configurações
│   ├── classes/           # Classes PHP
│   ├── endpoints/         # Endpoints da API
│   └── router.php         # Roteador
├── css/                   # Estilos
├── js/                    # JavaScript Frontend
├── pages/                 # Páginas HTML
├── assets/                # Recursos estáticos
├── .htaccess             # Configuração Apache
├── index.html            # Página inicial
└── login.html            # Página de login
";
echo "</pre>";
echo "</div>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    line-height: 1.6;
}

h1 {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 30px;
}

h2 {
    color: #007bff;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
    margin-top: 30px;
}

h3 {
    color: #495057;
    margin-top: 20px;
}

ul, ol {
    background: white;
    padding: 15px 30px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #007bff;
    overflow-x: auto;
    font-size: 14px;
}

a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
