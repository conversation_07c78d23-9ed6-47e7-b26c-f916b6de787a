<?php
/**
 * Endpoints de Agendamentos
 * Sistema Saúde Flex - Agendamentos
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/JWT.php';
require_once __DIR__ . '/../classes/Agendamento.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';
$id = $_GET['id'] ?? null;

$agendamento = new Agendamento();

switch ($method) {
    case 'GET':
        if ($id) {
            getAgendamento($id);
        } elseif ($path === 'hoje') {
            getAgendamentosHoje();
        } elseif ($path === 'proximos') {
            getProximosAgendamentos();
        } elseif ($path === 'periodo') {
            getAgendamentosPorPeriodo();
        } elseif ($path === 'cliente') {
            getAgendamentosPorCliente();
        } elseif ($path === 'funcionario') {
            getAgendamentosPorFuncionario();
        } elseif ($path === 'calendar') {
            getCalendarEvents();
        } elseif ($path === 'stats') {
            getEstatisticas();
        } elseif ($path === 'check-conflict') {
            checkConflict();
        } else {
            getAgendamentos();
        }
        break;
        
    case 'POST':
        createAgendamento();
        break;
        
    case 'PUT':
        if ($id) {
            if ($path === 'status') {
                updateStatus($id);
            } else {
                updateAgendamento($id);
            }
        } else {
            jsonResponse(['error' => 'ID do agendamento é obrigatório'], 400);
        }
        break;
        
    case 'DELETE':
        if ($id) {
            deleteAgendamento($id);
        } else {
            jsonResponse(['error' => 'ID do agendamento é obrigatório'], 400);
        }
        break;
        
    default:
        jsonResponse(['error' => 'Método não permitido'], 405);
}

/**
 * Listar todos os agendamentos
 */
function getAgendamentos() {
    global $agendamento;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $agendamentos = $agendamento->findWithRelations();
    
    jsonResponse(['agendamentos' => $agendamentos]);
}

/**
 * Buscar agendamento por ID
 */
function getAgendamento($id) {
    global $agendamento;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $agendamentoData = $agendamento->findById($id);
    
    if (!$agendamentoData) {
        jsonResponse(['error' => 'Agendamento não encontrado'], 404);
    }
    
    jsonResponse($agendamentoData);
}

/**
 * Criar novo agendamento
 */
function createAgendamento() {
    global $agendamento;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Dados inválidos'], 400);
    }
    
    // Sanitizar dados
    $data = sanitizeInput($input);
    
    // Validar dados
    $errors = $agendamento->validar($data);
    
    if (!empty($errors)) {
        jsonResponse(['error' => 'Dados inválidos', 'details' => $errors], 400);
    }
    
    // Verificar conflitos de horário
    if ($agendamento->hasConflict($data)) {
        jsonResponse(['error' => 'Conflito de horário: já existe um agendamento para este funcionário no horário especificado'], 409);
    }
    
    $id = $agendamento->create($data);
    
    if ($id) {
        jsonResponse(['id' => $id, 'message' => 'Agendamento criado com sucesso!'], 201);
    } else {
        jsonResponse(['error' => 'Erro ao criar agendamento'], 500);
    }
}

/**
 * Atualizar agendamento
 */
function updateAgendamento($id) {
    global $agendamento;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Dados inválidos'], 400);
    }
    
    // Verificar se agendamento existe
    if (!$agendamento->findById($id)) {
        jsonResponse(['error' => 'Agendamento não encontrado'], 404);
    }
    
    // Sanitizar dados
    $data = sanitizeInput($input);
    
    // Validar dados
    $errors = $agendamento->validar($data, true);
    
    if (!empty($errors)) {
        jsonResponse(['error' => 'Dados inválidos', 'details' => $errors], 400);
    }
    
    // Verificar conflitos de horário (excluindo o próprio agendamento)
    if ($agendamento->hasConflict($data, $id)) {
        jsonResponse(['error' => 'Conflito de horário: já existe um agendamento para este funcionário no horário especificado'], 409);
    }
    
    $result = $agendamento->update($id, $data);
    
    if ($result) {
        jsonResponse(['message' => 'Agendamento atualizado com sucesso!']);
    } else {
        jsonResponse(['error' => 'Erro ao atualizar agendamento'], 500);
    }
}

/**
 * Atualizar status do agendamento
 */
function updateStatus($id) {
    global $agendamento;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['status'])) {
        jsonResponse(['error' => 'Status é obrigatório'], 400);
    }
    
    // Verificar se agendamento existe
    if (!$agendamento->findById($id)) {
        jsonResponse(['error' => 'Agendamento não encontrado'], 404);
    }
    
    $result = $agendamento->updateStatus($id, $input['status']);
    
    if ($result) {
        jsonResponse(['message' => 'Status atualizado com sucesso!']);
    } else {
        jsonResponse(['error' => 'Erro ao atualizar status ou status inválido'], 400);
    }
}

/**
 * Excluir agendamento
 */
function deleteAgendamento($id) {
    global $agendamento;
    
    // Verificar autenticação e permissão
    JWT::checkPermission(['admin', 'gerente']);
    
    // Verificar se agendamento existe
    if (!$agendamento->findById($id)) {
        jsonResponse(['error' => 'Agendamento não encontrado'], 404);
    }
    
    $result = $agendamento->delete($id);
    
    if ($result) {
        jsonResponse(['message' => 'Agendamento excluído com sucesso!']);
    } else {
        jsonResponse(['error' => 'Erro ao excluir agendamento'], 500);
    }
}

/**
 * Obter agendamentos de hoje
 */
function getAgendamentosHoje() {
    global $agendamento;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $agendamentos = $agendamento->findHoje();
    
    jsonResponse(['agendamentos' => $agendamentos]);
}

/**
 * Obter próximos agendamentos
 */
function getProximosAgendamentos() {
    global $agendamento;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $limite = $_GET['limite'] ?? 10;
    $agendamentos = $agendamento->findProximos($limite);
    
    jsonResponse(['agendamentos' => $agendamentos]);
}

/**
 * Obter agendamentos por período
 */
function getAgendamentosPorPeriodo() {
    global $agendamento;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $dataInicio = $_GET['data_inicio'] ?? '';
    $dataFim = $_GET['data_fim'] ?? '';
    $funcionarioId = $_GET['funcionario_id'] ?? null;
    
    if (empty($dataInicio) || empty($dataFim)) {
        jsonResponse(['error' => 'Data de início e fim são obrigatórias'], 400);
    }
    
    $agendamentos = $agendamento->findByPeriodo($dataInicio, $dataFim, $funcionarioId);
    
    jsonResponse(['agendamentos' => $agendamentos]);
}

/**
 * Obter agendamentos por cliente
 */
function getAgendamentosPorCliente() {
    global $agendamento;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $clienteId = $_GET['cliente_id'] ?? '';
    
    if (empty($clienteId)) {
        jsonResponse(['error' => 'ID do cliente é obrigatório'], 400);
    }
    
    $agendamentos = $agendamento->findByCliente($clienteId);
    
    jsonResponse(['agendamentos' => $agendamentos]);
}

/**
 * Obter agendamentos por funcionário
 */
function getAgendamentosPorFuncionario() {
    global $agendamento;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $funcionarioId = $_GET['funcionario_id'] ?? '';
    $dataInicio = $_GET['data_inicio'] ?? null;
    $dataFim = $_GET['data_fim'] ?? null;
    
    if (empty($funcionarioId)) {
        jsonResponse(['error' => 'ID do funcionário é obrigatório'], 400);
    }
    
    $agendamentos = $agendamento->findByFuncionario($funcionarioId, $dataInicio, $dataFim);
    
    jsonResponse(['agendamentos' => $agendamentos]);
}

/**
 * Obter eventos para o calendário
 */
function getCalendarEvents() {
    global $agendamento;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $dataInicio = $_GET['start'] ?? '';
    $dataFim = $_GET['end'] ?? '';
    
    if (empty($dataInicio) || empty($dataFim)) {
        jsonResponse(['error' => 'Parâmetros start e end são obrigatórios'], 400);
    }
    
    $events = $agendamento->getCalendarEvents($dataInicio, $dataFim);
    
    jsonResponse($events);
}

/**
 * Verificar conflitos de horário
 */
function checkConflict() {
    global $agendamento;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $funcionarioId = $_GET['funcionario_id'] ?? '';
    $dataVisita = $_GET['data_visita'] ?? '';
    $horaInicio = $_GET['hora_inicio'] ?? '';
    $horaFim = $_GET['hora_fim'] ?? '';
    $excludeId = $_GET['exclude_id'] ?? null;
    
    if (empty($funcionarioId) || empty($dataVisita) || empty($horaInicio) || empty($horaFim)) {
        jsonResponse(['error' => 'Todos os parâmetros são obrigatórios'], 400);
    }
    
    $data = [
        'funcionario_id' => $funcionarioId,
        'data_visita' => $dataVisita,
        'hora_inicio' => $horaInicio,
        'hora_fim' => $horaFim
    ];
    
    $hasConflict = $agendamento->hasConflict($data, $excludeId);
    
    jsonResponse(['conflict' => $hasConflict]);
}

/**
 * Obter estatísticas de agendamentos
 */
function getEstatisticas() {
    global $agendamento;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $stats = $agendamento->getEstatisticas();
    
    jsonResponse($stats);
}
