<?php
/**
 * Endpoints de Clientes
 * Sistema Saúde Flex - Agendamentos
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/JWT.php';
require_once __DIR__ . '/../classes/Cliente.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';
$id = $_GET['id'] ?? null;

$cliente = new Cliente();

switch ($method) {
    case 'GET':
        if ($id) {
            getCliente($id);
        } elseif ($path === 'search') {
            searchClientes();
        } elseif ($path === 'stats') {
            getEstatisticas();
        } elseif ($path === 'aniversariantes') {
            getAniversariantes();
        } else {
            getClientes();
        }
        break;
        
    case 'POST':
        createCliente();
        break;
        
    case 'PUT':
        if ($id) {
            updateCliente($id);
        } else {
            jsonResponse(['error' => 'ID do cliente é obrigatório'], 400);
        }
        break;
        
    case 'DELETE':
        if ($id) {
            deleteCliente($id);
        } else {
            jsonResponse(['error' => 'ID do cliente é obrigatório'], 400);
        }
        break;
        
    default:
        jsonResponse(['error' => 'Método não permitido'], 405);
}

/**
 * Listar todos os clientes
 */
function getClientes() {
    global $cliente;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $clientes = $cliente->findAtivos();
    
    jsonResponse(['clientes' => $clientes]);
}

/**
 * Buscar cliente por ID
 */
function getCliente($id) {
    global $cliente;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $clienteData = $cliente->findById($id);
    
    if (!$clienteData) {
        jsonResponse(['error' => 'Cliente não encontrado'], 404);
    }
    
    // Buscar histórico de agendamentos
    $historico = $cliente->getHistoricoAgendamentos($id);
    $clienteData['historico_agendamentos'] = $historico;
    
    jsonResponse($clienteData);
}

/**
 * Criar novo cliente
 */
function createCliente() {
    global $cliente;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Dados inválidos'], 400);
    }
    
    // Sanitizar dados
    $data = sanitizeInput($input);
    
    // Validar dados
    $errors = $cliente->validar($data);
    
    if (!empty($errors)) {
        jsonResponse(['error' => 'Dados inválidos', 'details' => $errors], 400);
    }
    
    $id = $cliente->create($data);
    
    if ($id) {
        jsonResponse(['id' => $id, 'message' => 'Cliente cadastrado com sucesso!'], 201);
    } else {
        jsonResponse(['error' => 'Erro ao cadastrar cliente'], 500);
    }
}

/**
 * Atualizar cliente
 */
function updateCliente($id) {
    global $cliente;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Dados inválidos'], 400);
    }
    
    // Verificar se cliente existe
    if (!$cliente->findById($id)) {
        jsonResponse(['error' => 'Cliente não encontrado'], 404);
    }
    
    // Sanitizar dados
    $data = sanitizeInput($input);
    $data['id'] = $id; // Para validação de email único
    
    // Validar dados
    $errors = $cliente->validar($data, true);
    
    if (!empty($errors)) {
        jsonResponse(['error' => 'Dados inválidos', 'details' => $errors], 400);
    }
    
    unset($data['id']); // Remover ID dos dados de atualização
    
    $result = $cliente->update($id, $data);
    
    if ($result) {
        jsonResponse(['message' => 'Cliente atualizado com sucesso!']);
    } else {
        jsonResponse(['error' => 'Erro ao atualizar cliente'], 500);
    }
}

/**
 * Excluir cliente
 */
function deleteCliente($id) {
    global $cliente;
    
    // Verificar autenticação e permissão
    JWT::checkPermission(['admin', 'gerente']);
    
    // Verificar se cliente existe
    if (!$cliente->findById($id)) {
        jsonResponse(['error' => 'Cliente não encontrado'], 404);
    }
    
    $result = $cliente->delete($id);
    
    if ($result) {
        jsonResponse(['message' => 'Cliente removido com sucesso!']);
    } else {
        jsonResponse(['error' => 'Erro ao remover cliente'], 500);
    }
}

/**
 * Pesquisar clientes
 */
function searchClientes() {
    global $cliente;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $termo = $_GET['q'] ?? '';
    
    if (empty($termo)) {
        jsonResponse(['error' => 'Termo de pesquisa é obrigatório'], 400);
    }
    
    $resultados = $cliente->search($termo);
    
    jsonResponse(['clientes' => $resultados]);
}

/**
 * Obter estatísticas de clientes
 */
function getEstatisticas() {
    global $cliente;
    
    // Verificar autenticação
    JWT::checkPermission(['admin', 'gerente']);
    
    $stats = $cliente->getEstatisticas();
    
    jsonResponse($stats);
}

/**
 * Obter aniversariantes do mês
 */
function getAniversariantes() {
    global $cliente;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $mes = $_GET['mes'] ?? null;
    $aniversariantes = $cliente->aniversariantesDoMes($mes);
    
    jsonResponse(['aniversariantes' => $aniversariantes]);
}
