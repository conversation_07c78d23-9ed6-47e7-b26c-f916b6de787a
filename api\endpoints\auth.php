<?php
/**
 * Endpoints de Autenticação
 * Sistema Saúde Flex - Agendamentos
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/JWT.php';
require_once __DIR__ . '/../classes/Usuario.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';

$usuario = new Usuario();

switch ($method) {
    case 'POST':
        if ($path === 'login') {
            login();
        } elseif ($path === 'logout') {
            logout();
        } else {
            jsonResponse(['error' => 'Endpoint não encontrado'], 404);
        }
        break;
        
    case 'GET':
        if ($path === 'validate') {
            validateToken();
        } elseif ($path === 'me') {
            getCurrentUser();
        } else {
            jsonResponse(['error' => 'Endpoint não encontrado'], 404);
        }
        break;
        
    default:
        jsonResponse(['error' => 'Método não permitido'], 405);
}

/**
 * Login do usuário
 */
function login() {
    global $usuario;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Dados inválidos'], 400);
    }
    
    $email = $input['email'] ?? '';
    $senha = $input['senha'] ?? '';
    
    if (empty($email) || empty($senha)) {
        jsonResponse(['error' => 'Email e senha são obrigatórios'], 400);
    }
    
    $userData = $usuario->verificarCredenciais($email, $senha);
    
    if (!$userData) {
        jsonResponse(['error' => 'Credenciais inválidas'], 401);
    }
    
    // Gerar token JWT
    $payload = [
        'id' => $userData['id'],
        'email' => $userData['email'],
        'nome' => $userData['nome'],
        'tipo' => $userData['tipo'],
        'iat' => time(),
        'exp' => time() + JWT_EXPIRATION
    ];
    
    $token = JWT::encode($payload);
    
    jsonResponse([
        'message' => 'Login realizado com sucesso',
        'token' => $token,
        'user' => $userData
    ]);
}

/**
 * Logout do usuário
 */
function logout() {
    // Verificar se está autenticado
    JWT::authenticate();
    
    jsonResponse(['message' => 'Logout realizado com sucesso']);
}

/**
 * Validar token
 */
function validateToken() {
    $user = JWT::authenticate();
    
    jsonResponse([
        'valid' => true,
        'user' => $user
    ]);
}

/**
 * Obter dados do usuário atual
 */
function getCurrentUser() {
    global $usuario;
    
    $userToken = JWT::authenticate();
    $userData = $usuario->findById($userToken['id']);
    
    if (!$userData) {
        jsonResponse(['error' => 'Usuário não encontrado'], 404);
    }
    
    // Remover senha
    unset($userData['senha']);
    
    jsonResponse($userData);
}
