<?php
/**
 * Endpoints do Dashboard
 * Sistema Saúde Flex - Agendamentos
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/JWT.php';
require_once __DIR__ . '/../classes/Agendamento.php';
require_once __DIR__ . '/../classes/Cliente.php';
require_once __DIR__ . '/../classes/Produto.php';
require_once __DIR__ . '/../classes/Usuario.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';

switch ($method) {
    case 'GET':
        if ($path === 'stats') {
            getDashboardStats();
        } elseif ($path === 'chart-agendamentos') {
            getChartAgendamentos();
        } elseif ($path === 'chart-receita') {
            getChartReceita();
        } elseif ($path === 'resumo-mensal') {
            getResumoMensal();
        } else {
            jsonResponse(['error' => 'Endpoint não encontrado'], 404);
        }
        break;
        
    default:
        jsonResponse(['error' => 'Método não permitido'], 405);
}

/**
 * Obter estatísticas principais do dashboard
 */
function getDashboardStats() {
    // Verificar autenticação
    JWT::authenticate();
    
    $agendamento = new Agendamento();
    $cliente = new Cliente();
    $produto = new Produto();
    $usuario = new Usuario();
    
    $stats = [];
    
    // Estatísticas de agendamentos
    $agendamentoStats = $agendamento->getEstatisticas();
    $stats['agendamentos'] = [
        'hoje' => $agendamentoStats['hoje'],
        'mes' => $agendamentoStats['mes'],
        'receita_mes' => $agendamentoStats['receita_mes']
    ];
    
    // Total de clientes ativos
    $stats['clientes'] = [
        'total' => $cliente->count(['status' => 'ativo'])
    ];
    
    // Produtos com estoque baixo
    $produtosEstoqueBaixo = $produto->findEstoqueBaixo();
    $stats['produtos'] = [
        'estoque_baixo' => count($produtosEstoqueBaixo),
        'total' => $produto->count(['status' => 'ativo'])
    ];
    
    // Próximos agendamentos
    $stats['proximos_agendamentos'] = $agendamento->findProximos(5);
    
    // Agendamentos de hoje
    $stats['agendamentos_hoje'] = $agendamento->findHoje();
    
    jsonResponse($stats);
}

/**
 * Obter dados para gráfico de agendamentos
 */
function getChartAgendamentos() {
    // Verificar autenticação
    JWT::authenticate();
    
    $agendamento = new Agendamento();
    
    // Últimos 30 dias
    $sql = "SELECT DATE(data_visita) as data, COUNT(*) as total 
            FROM agendamentos 
            WHERE data_visita >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            AND data_visita <= CURDATE()
            GROUP BY DATE(data_visita)
            ORDER BY data_visita ASC";
    
    $dados = $agendamento->query($sql);
    
    // Preencher dias sem agendamentos
    $resultado = [];
    $dataAtual = date('Y-m-d', strtotime('-30 days'));
    $dataFim = date('Y-m-d');
    
    while ($dataAtual <= $dataFim) {
        $total = 0;
        foreach ($dados as $item) {
            if ($item['data'] === $dataAtual) {
                $total = $item['total'];
                break;
            }
        }
        
        $resultado[] = [
            'data' => $dataAtual,
            'total' => $total,
            'data_formatada' => date('d/m', strtotime($dataAtual))
        ];
        
        $dataAtual = date('Y-m-d', strtotime($dataAtual . ' +1 day'));
    }
    
    jsonResponse(['dados' => $resultado]);
}

/**
 * Obter dados para gráfico de receita
 */
function getChartReceita() {
    // Verificar autenticação
    JWT::authenticate();
    
    $agendamento = new Agendamento();
    
    // Últimos 12 meses
    $sql = "SELECT 
                YEAR(data_visita) as ano,
                MONTH(data_visita) as mes,
                SUM(valor) as receita
            FROM agendamentos 
            WHERE data_visita >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
            AND status IN ('concluido', 'confirmado')
            GROUP BY YEAR(data_visita), MONTH(data_visita)
            ORDER BY ano ASC, mes ASC";
    
    $dados = $agendamento->query($sql);
    
    // Formatar dados
    $resultado = [];
    foreach ($dados as $item) {
        $mesNome = [
            1 => 'Jan', 2 => 'Fev', 3 => 'Mar', 4 => 'Abr',
            5 => 'Mai', 6 => 'Jun', 7 => 'Jul', 8 => 'Ago',
            9 => 'Set', 10 => 'Out', 11 => 'Nov', 12 => 'Dez'
        ];
        
        $resultado[] = [
            'periodo' => $mesNome[$item['mes']] . '/' . $item['ano'],
            'receita' => floatval($item['receita'])
        ];
    }
    
    jsonResponse(['dados' => $resultado]);
}

/**
 * Obter resumo mensal detalhado
 */
function getResumoMensal() {
    // Verificar autenticação
    JWT::authenticate();
    
    $agendamento = new Agendamento();
    $cliente = new Cliente();
    $produto = new Produto();
    
    $mesAtual = date('Y-m');
    $mesAnterior = date('Y-m', strtotime('-1 month'));
    
    $resumo = [];
    
    // Agendamentos do mês atual vs anterior
    $sql = "SELECT 
                SUM(CASE WHEN DATE_FORMAT(data_visita, '%Y-%m') = ? THEN 1 ELSE 0 END) as atual,
                SUM(CASE WHEN DATE_FORMAT(data_visita, '%Y-%m') = ? THEN 1 ELSE 0 END) as anterior
            FROM agendamentos";
    
    $result = $agendamento->queryOne($sql, [$mesAtual, $mesAnterior]);
    $resumo['agendamentos'] = [
        'atual' => intval($result['atual']),
        'anterior' => intval($result['anterior']),
        'variacao' => $result['anterior'] > 0 ? 
            round((($result['atual'] - $result['anterior']) / $result['anterior']) * 100, 1) : 0
    ];
    
    // Receita do mês atual vs anterior
    $sql = "SELECT 
                SUM(CASE WHEN DATE_FORMAT(data_visita, '%Y-%m') = ? AND status IN ('concluido', 'confirmado') THEN valor ELSE 0 END) as atual,
                SUM(CASE WHEN DATE_FORMAT(data_visita, '%Y-%m') = ? AND status IN ('concluido', 'confirmado') THEN valor ELSE 0 END) as anterior
            FROM agendamentos";
    
    $result = $agendamento->queryOne($sql, [$mesAtual, $mesAnterior]);
    $resumo['receita'] = [
        'atual' => floatval($result['atual']),
        'anterior' => floatval($result['anterior']),
        'variacao' => $result['anterior'] > 0 ? 
            round((($result['atual'] - $result['anterior']) / $result['anterior']) * 100, 1) : 0
    ];
    
    // Novos clientes do mês
    $sql = "SELECT COUNT(*) FROM clientes 
            WHERE DATE_FORMAT(data_cadastro, '%Y-%m') = ? 
            AND status = 'ativo'";
    
    $resumo['novos_clientes'] = $cliente->queryValue($sql, [$mesAtual]);
    
    // Produtos mais agendados do mês
    $sql = "SELECT p.nome, COUNT(a.id) as total
            FROM produtos p
            LEFT JOIN agendamentos a ON p.id = a.produto_id
                AND DATE_FORMAT(a.data_visita, '%Y-%m') = ?
            WHERE p.status = 'ativo'
            GROUP BY p.id, p.nome
            HAVING total > 0
            ORDER BY total DESC
            LIMIT 5";
    
    $resumo['produtos_populares'] = $agendamento->query($sql, [$mesAtual]);
    
    // Status dos agendamentos do mês
    $sql = "SELECT status, COUNT(*) as total
            FROM agendamentos
            WHERE DATE_FORMAT(data_visita, '%Y-%m') = ?
            GROUP BY status";
    
    $statusData = $agendamento->query($sql, [$mesAtual]);
    $resumo['status_agendamentos'] = [];
    
    foreach ($statusData as $status) {
        $resumo['status_agendamentos'][$status['status']] = intval($status['total']);
    }
    
    // Funcionários com mais agendamentos do mês
    $sql = "SELECT f.nome, COUNT(a.id) as total
            FROM funcionarios f
            LEFT JOIN agendamentos a ON f.id = a.funcionario_id
                AND DATE_FORMAT(a.data_visita, '%Y-%m') = ?
            WHERE f.status = 'ativo'
            GROUP BY f.id, f.nome
            HAVING total > 0
            ORDER BY total DESC
            LIMIT 5";
    
    $resumo['funcionarios_destaque'] = $agendamento->query($sql, [$mesAtual]);
    
    jsonResponse($resumo);
}
