# Sistema Saúde Flex - Versão PHP + MariaDB

Sistema avançado de agendamentos para empresas de saúde e bem-estar, desenvolvido em HTML, CSS, JavaScript puro e PHP com banco de dados MariaDB/MySQL.

## 🚀 Tecnologias Utilizadas

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: PHP 7.4+
- **Banco de Dados**: MariaDB/MySQL 5.7+
- **Servidor Web**: Apache/Nginx

## 📋 Pré-requisitos

- PHP 7.4 ou superior
- MariaDB 10.3+ ou MySQL 5.7+
- Servidor web (Apache/Nginx)
- Extensões PHP necessárias:
  - PDO
  - PDO_MySQL
  - JSON
  - OpenSSL

## 🔧 Instalação

### 1. Configurar o Banco de Dados

```sql
-- Executar o script SQL
mysql -u root -p < database_setup.sql
```

Ou importar manualmente o arquivo `database_setup.sql` no seu gerenciador de banco de dados.

### 2. Configurar o PHP

Edite o arquivo `api/config/database.php` com suas credenciais:

```php
private $host = 'localhost';
private $db_name = 'saude_flex';
private $username = 'seu_usuario';
private $password = 'sua_senha';
```

### 3. Configurar o Servidor Web

#### Apache (.htaccess já configurado)

Certifique-se de que o mod_rewrite está habilitado:

```bash
sudo a2enmod rewrite
sudo systemctl restart apache2
```

#### Nginx

Adicione ao seu arquivo de configuração:

```nginx
location /api/ {
    try_files $uri $uri/ /api/index.php?$query_string;
}

location ~ \.php$ {
    fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    include fastcgi_params;
}
```

### 4. Configurar Permissões

```bash
# Dar permissões de escrita para logs (se necessário)
chmod 755 api/
chmod 644 api/config/*
```

## 🎯 Estrutura do Projeto

```
saude-flex/
├── api/                    # Backend PHP
│   ├── config/            # Configurações
│   │   ├── config.php     # Configurações gerais
│   │   └── database.php   # Conexão com banco
│   ├── classes/           # Classes do sistema
│   │   ├── BaseModel.php  # Classe base para modelos
│   │   ├── JWT.php        # Autenticação JWT
│   │   ├── Usuario.php    # Modelo de usuários
│   │   └── Cliente.php    # Modelo de clientes
│   ├── endpoints/         # Endpoints da API
│   │   ├── auth.php       # Autenticação
│   │   ├── usuarios.php   # Usuários
│   │   └── clientes.php   # Clientes
│   ├── .htaccess         # Configuração Apache
│   └── index.php         # Roteador principal
├── css/                   # Estilos CSS
├── js/                    # JavaScript
│   ├── api.js            # Cliente da API
│   ├── app.js            # Aplicação principal
│   └── utils.js          # Utilitários
├── pages/                 # Páginas HTML
├── assets/               # Recursos (imagens, ícones)
├── index.html            # Página principal
├── login.html            # Página de login
└── database_setup.sql    # Script de criação do banco
```

## 🔐 Credenciais Padrão

- **Email**: <EMAIL>
- **Senha**: admin123

⚠️ **Importante**: Altere essas credenciais após o primeiro login!

## 📡 Endpoints da API

### Autenticação
- `POST /api/auth/login` - Login
- `POST /api/auth/logout` - Logout
- `GET /api/auth/validate` - Validar token
- `GET /api/auth/me` - Dados do usuário atual

### Usuários (Admin apenas)
- `GET /api/usuarios` - Listar usuários
- `POST /api/usuarios` - Criar usuário
- `PUT /api/usuarios/{id}` - Atualizar usuário
- `DELETE /api/usuarios/{id}` - Excluir usuário

### Clientes
- `GET /api/clientes` - Listar clientes
- `GET /api/clientes/{id}` - Buscar cliente
- `POST /api/clientes` - Criar cliente
- `PUT /api/clientes/{id}` - Atualizar cliente
- `DELETE /api/clientes/{id}` - Excluir cliente
- `GET /api/clientes/search?q={termo}` - Pesquisar clientes

## 🛡️ Segurança

- Autenticação JWT
- Senhas criptografadas com bcrypt
- Proteção contra SQL Injection (PDO Prepared Statements)
- Validação de dados de entrada
- Controle de permissões por tipo de usuário
- Headers de segurança CORS configurados

## 🔧 Desenvolvimento

### Estrutura de Permissões

- **Admin**: Acesso total ao sistema
- **Gerente**: Gerencia produtos, vendas, agendamentos e clientes
- **Vendedor**: Apenas vendas, agendamentos e cadastro de clientes

### Adicionando Novos Endpoints

1. Criar modelo em `api/classes/`
2. Criar endpoint em `api/endpoints/`
3. Adicionar rota em `api/index.php`
4. Atualizar `js/api.js` se necessário

## 🐛 Troubleshooting

### Erro de Conexão com Banco
- Verificar credenciais em `api/config/database.php`
- Confirmar se o banco `saude_flex` existe
- Verificar se o usuário tem permissões adequadas

### Erro 404 nas APIs
- Verificar se mod_rewrite está habilitado (Apache)
- Confirmar configuração do Nginx
- Verificar permissões dos arquivos

### Token JWT Inválido
- Verificar se a constante JWT_SECRET está definida
- Confirmar se o token está sendo enviado no header Authorization
- Verificar se o token não expirou (24h por padrão)

## 📝 Logs

Os logs de erro são gravados no log padrão do PHP. Para debug, você pode habilitar a exibição de erros em `api/config/config.php`:

```php
ini_set('display_errors', 1); // Apenas em desenvolvimento
```

## 🚀 Deploy em Produção

1. Configurar HTTPS
2. Alterar credenciais padrão
3. Configurar backup automático do banco
4. Desabilitar exibição de erros PHP
5. Configurar logs adequados
6. Implementar monitoramento

## 📞 Suporte

Para dúvidas ou problemas, consulte a documentação ou entre em contato com a equipe de desenvolvimento.
