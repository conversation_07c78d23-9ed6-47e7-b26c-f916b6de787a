<?php
/**
 * Teste de Conexão com MariaDB
 * Sistema Saúde Flex - Agendamentos
 */

require_once 'api/config/config.php';
require_once 'api/config/database.php';

echo "<h1>Teste de Conexão - Sistema Saúde Flex</h1>";

try {
    echo "<h2>1. Testando Conexão com MariaDB...</h2>";
    
    $database = new Database();
    $conn = $database->getConnection();
    
    if ($conn) {
        echo "✅ <strong>Conexão com MariaDB estabelecida com sucesso!</strong><br>";
        
        // Testar se o banco existe
        $stmt = $conn->query("SELECT DATABASE() as db_name");
        $result = $stmt->fetch();
        echo "📊 Banco de dados atual: <strong>" . $result['db_name'] . "</strong><br>";
        
        echo "<h2>2. Verificando Tabelas...</h2>";
        
        // Listar tabelas
        $stmt = $conn->query("SHOW TABLES");
        $tables = $stmt->fetchAll();
        
        if (count($tables) > 0) {
            echo "✅ <strong>Tabelas encontradas:</strong><br>";
            foreach ($tables as $table) {
                $tableName = array_values($table)[0];
                
                // Contar registros
                $countStmt = $conn->query("SELECT COUNT(*) as total FROM `$tableName`");
                $count = $countStmt->fetch()['total'];
                
                echo "📋 $tableName ($count registros)<br>";
            }
        } else {
            echo "⚠️ Nenhuma tabela encontrada. Criando estrutura...<br>";
        }
        
        echo "<h2>3. Testando Usuário Admin...</h2>";
        
        // Verificar usuário admin
        $stmt = $conn->prepare("SELECT id, nome, email, tipo FROM usuarios WHERE tipo = 'admin' LIMIT 1");
        $stmt->execute();
        $admin = $stmt->fetch();
        
        if ($admin) {
            echo "✅ <strong>Usuário administrador encontrado:</strong><br>";
            echo "👤 ID: " . $admin['id'] . "<br>";
            echo "👤 Nome: " . $admin['nome'] . "<br>";
            echo "📧 Email: " . $admin['email'] . "<br>";
            echo "🔑 Tipo: " . $admin['tipo'] . "<br>";
        } else {
            echo "⚠️ Usuário administrador não encontrado.<br>";
        }
        
        echo "<h2>4. Testando Produtos...</h2>";
        
        // Verificar produtos
        $stmt = $conn->query("SELECT COUNT(*) as total FROM produtos");
        $produtoCount = $stmt->fetch()['total'];
        
        if ($produtoCount > 0) {
            echo "✅ <strong>$produtoCount produtos encontrados</strong><br>";
            
            // Mostrar alguns produtos
            $stmt = $conn->query("SELECT nome, preco_venda, categoria FROM produtos LIMIT 3");
            $produtos = $stmt->fetchAll();
            
            foreach ($produtos as $produto) {
                echo "🛍️ " . $produto['nome'] . " - R$ " . number_format($produto['preco_venda'], 2, ',', '.') . " (" . $produto['categoria'] . ")<br>";
            }
        } else {
            echo "⚠️ Nenhum produto encontrado.<br>";
        }
        
        echo "<h2>5. Testando Funcionários...</h2>";
        
        // Verificar funcionários
        $stmt = $conn->query("SELECT COUNT(*) as total FROM funcionarios");
        $funcCount = $stmt->fetch()['total'];
        
        if ($funcCount > 0) {
            echo "✅ <strong>$funcCount funcionários encontrados</strong><br>";
            
            // Mostrar alguns funcionários
            $stmt = $conn->query("SELECT nome, cargo FROM funcionarios LIMIT 3");
            $funcionarios = $stmt->fetchAll();
            
            foreach ($funcionarios as $func) {
                echo "👨‍💼 " . $func['nome'] . " - " . $func['cargo'] . "<br>";
            }
        } else {
            echo "⚠️ Nenhum funcionário encontrado.<br>";
        }
        
        echo "<h2>✅ Teste Concluído com Sucesso!</h2>";
        echo "<p><strong>O sistema está pronto para uso!</strong></p>";
        echo "<p>📝 <strong>Credenciais de acesso:</strong></p>";
        echo "<p>Email: <EMAIL><br>Senha: admin123</p>";
        echo "<p><a href='login.html' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Acessar Sistema</a></p>";
        
    } else {
        echo "❌ <strong>Erro: Não foi possível conectar ao MariaDB</strong><br>";
    }
    
} catch (Exception $e) {
    echo "❌ <strong>Erro na conexão:</strong> " . $e->getMessage() . "<br>";
    echo "<p>Verifique se:</p>";
    echo "<ul>";
    echo "<li>O MariaDB/MySQL está instalado e rodando</li>";
    echo "<li>As credenciais em api/config/database.php estão corretas</li>";
    echo "<li>O usuário tem permissões para criar bancos de dados</li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}

h1 {
    color: #333;
    text-align: center;
    background: #007bff;
    color: white;
    padding: 20px;
    border-radius: 10px;
}

h2 {
    color: #007bff;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

p, li {
    line-height: 1.6;
}

ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #dc3545;
}
</style>
