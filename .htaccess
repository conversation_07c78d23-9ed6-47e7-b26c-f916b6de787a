# Configuração Apache para Sistema Saúde Flex
# Migração Node.js → PHP + MariaDB

# Habilitar rewrite engine
RewriteEngine On

# Configurações de segurança
ServerTokens Prod
ServerSignature Off

# Headers de segurança
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# CORS Headers para API
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"

# Responder a requisições OPTIONS (preflight)
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# Roteamento da API PHP
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/router.php [QSA,L]

# Página inicial padrão
DirectoryIndex index.html login.html

# Configurações de cache
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Cache para arquivos estáticos
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    
    # Não cachear arquivos da API
    ExpiresByType application/json "access plus 0 seconds"
</IfModule>

# Compressão GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Configurações de upload
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300

# Configurações de sessão
php_value session.cookie_httponly 1
php_value session.use_only_cookies 1
php_value session.cookie_secure 0

# Ocultar informações do PHP
php_flag expose_php off

# Configurações de erro
php_flag display_errors off
php_flag log_errors on
php_value error_log "logs/php_errors.log"

# Proteger arquivos sensíveis
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files "*.env">
    Order allow,deny
    Deny from all
</Files>

# Proteger diretórios
<Directory "api/config">
    Order allow,deny
    Allow from all
</Directory>

<Directory "logs">
    Order allow,deny
    Deny from all
</Directory>

<Directory "backups">
    Order allow,deny
    Deny from all
</Directory>

# Redirecionamentos para compatibilidade
# Redirecionar rotas antigas do Node.js para PHP

# Auth endpoints
RewriteRule ^auth/login$ api/endpoints/auth.php [QSA,L]
RewriteRule ^auth/logout$ api/endpoints/auth.php [QSA,L]
RewriteRule ^auth/validate$ api/endpoints/auth.php [QSA,L]
RewriteRule ^auth/me$ api/endpoints/auth.php [QSA,L]

# Clientes endpoints
RewriteRule ^clientes/?$ api/endpoints/clientes.php [QSA,L]
RewriteRule ^clientes/([0-9]+)/?$ api/endpoints/clientes.php?id=$1 [QSA,L]

# Produtos endpoints
RewriteRule ^produtos/?$ api/endpoints/produtos.php [QSA,L]
RewriteRule ^produtos/([0-9]+)/?$ api/endpoints/produtos.php?id=$1 [QSA,L]

# Funcionários endpoints
RewriteRule ^funcionarios/?$ api/endpoints/funcionarios.php [QSA,L]
RewriteRule ^funcionarios/([0-9]+)/?$ api/endpoints/funcionarios.php?id=$1 [QSA,L]

# Agendamentos endpoints
RewriteRule ^agendamentos/?$ api/endpoints/agendamentos.php [QSA,L]
RewriteRule ^agendamentos/([0-9]+)/?$ api/endpoints/agendamentos.php?id=$1 [QSA,L]
RewriteRule ^agendamentos/([0-9]+)/status/?$ api/endpoints/agendamentos.php?id=$1&action=status [QSA,L]

# Usuários endpoints
RewriteRule ^usuarios/?$ api/endpoints/usuarios.php [QSA,L]
RewriteRule ^usuarios/([0-9]+)/?$ api/endpoints/usuarios.php?id=$1 [QSA,L]
RewriteRule ^usuarios/vendedores/?$ api/endpoints/usuarios.php?action=vendedores [QSA,L]

# Dashboard endpoints
RewriteRule ^dashboard/stats/?$ api/endpoints/dashboard.php [QSA,L]

# Configurações de tipos MIME
AddType application/json .json
AddType text/css .css
AddType application/javascript .js
