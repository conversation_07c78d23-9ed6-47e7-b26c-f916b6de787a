<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Saúde Flex</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: var(--font-family);
        }

        .login-container {
            background: white;
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            margin: 2rem;
        }

        .login-header {
            background: var(--primary-color);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .login-logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .login-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .login-subtitle {
            font-size: 0.875rem;
            opacity: 0.9;
            margin: 0.5rem 0 0;
        }

        .login-form {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-input.error {
            border-color: var(--error-color);
        }

        .error-message {
            color: var(--error-color);
            font-size: 0.875rem;
            margin-top: 0.5rem;
            display: none;
        }

        .login-button {
            width: 100%;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.875rem 1rem;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            margin-bottom: 1rem;
        }

        .login-button:hover {
            background: var(--primary-dark);
        }

        .login-button:disabled {
            background: var(--gray-400);
            cursor: not-allowed;
        }

        .login-footer {
            text-align: center;
            padding-top: 1rem;
            border-top: 1px solid var(--gray-200);
        }

        .forgot-password {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.875rem;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 1rem;
        }

        .loading-spinner {
            border: 2px solid var(--gray-200);
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .demo-credentials {
            background: var(--gray-50);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }

        .demo-title {
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
        }

        .demo-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.25rem;
            color: var(--gray-600);
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 1rem;
            }
            
            .login-header,
            .login-form {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-logo">
                <i class="fas fa-heartbeat"></i>
            </div>
            <h1 class="login-title">Saúde Flex</h1>
            <p class="login-subtitle">Sistema de Agendamentos</p>
        </div>

        <form class="login-form" id="loginForm">
            <div class="demo-credentials">
                <div class="demo-title">Credenciais de Demonstração:</div>
                <div class="demo-item">
                    <span>Email:</span>
                    <span><EMAIL></span>
                </div>
                <div class="demo-item">
                    <span>Senha:</span>
                    <span>admin123</span>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label" for="email">
                    <i class="fas fa-envelope"></i> Email
                </label>
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    class="form-input" 
                    placeholder="<EMAIL>"
                    value="<EMAIL>"
                    required
                >
                <div class="error-message" id="emailError"></div>
            </div>

            <div class="form-group">
                <label class="form-label" for="senha">
                    <i class="fas fa-lock"></i> Senha
                </label>
                <input 
                    type="password" 
                    id="senha" 
                    name="senha" 
                    class="form-input" 
                    placeholder="Sua senha"
                    value="admin123"
                    required
                >
                <div class="error-message" id="senhaError"></div>
            </div>

            <button type="submit" class="login-button" id="loginButton">
                <i class="fas fa-sign-in-alt"></i> Entrar
            </button>

            <div class="loading" id="loading">
                <div class="loading-spinner"></div>
                Verificando credenciais...
            </div>

            <div class="login-footer">
                <a href="#" class="forgot-password">Esqueceu sua senha?</a>
            </div>
        </form>
    </div>

    <!-- Container de notificações -->
    <div id="notifications-container"></div>

    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script>
        // Sistema de login
        class LoginSystem {
            constructor() {
                this.form = document.getElementById('loginForm');
                this.emailInput = document.getElementById('email');
                this.senhaInput = document.getElementById('senha');
                this.loginButton = document.getElementById('loginButton');
                this.loading = document.getElementById('loading');
                
                this.init();
            }

            init() {
                // Verificar se já está logado
                if (this.isLoggedIn()) {
                    window.location.href = 'index.html';
                    return;
                }

                // Configurar eventos
                this.form.addEventListener('submit', (e) => this.handleLogin(e));
                
                // Validação em tempo real
                this.emailInput.addEventListener('blur', () => this.validateEmail());
                this.senhaInput.addEventListener('blur', () => this.validatePassword());
                
                // Limpar erros ao digitar
                this.emailInput.addEventListener('input', () => this.clearError('email'));
                this.senhaInput.addEventListener('input', () => this.clearError('senha'));
            }

            async handleLogin(e) {
                e.preventDefault();
                
                if (!this.validateForm()) {
                    return;
                }

                this.setLoading(true);

                try {
                    const formData = new FormData(this.form);
                    const credentials = {
                        email: formData.get('email'),
                        senha: formData.get('senha')
                    };

                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(credentials)
                    });

                    const result = await response.json();

                    if (response.ok) {
                        // Salvar dados do usuário
                        localStorage.setItem('user', JSON.stringify(result.user));
                        localStorage.setItem('token', result.token);
                        
                        notifications.success('Login realizado com sucesso!');
                        
                        // Redirecionar após um breve delay
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 1000);
                    } else {
                        notifications.error(result.error || 'Erro ao fazer login');
                        this.setLoading(false);
                    }
                } catch (error) {
                    console.error('Erro no login:', error);
                    notifications.error('Erro de conexão. Tente novamente.');
                    this.setLoading(false);
                }
            }

            validateForm() {
                let isValid = true;
                
                if (!this.validateEmail()) isValid = false;
                if (!this.validatePassword()) isValid = false;
                
                return isValid;
            }

            validateEmail() {
                const email = this.emailInput.value.trim();
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                
                if (!email) {
                    this.showError('email', 'Email é obrigatório');
                    return false;
                } else if (!emailRegex.test(email)) {
                    this.showError('email', 'Email inválido');
                    return false;
                }
                
                this.clearError('email');
                return true;
            }

            validatePassword() {
                const senha = this.senhaInput.value.trim();
                
                if (!senha) {
                    this.showError('senha', 'Senha é obrigatória');
                    return false;
                } else if (senha.length < 6) {
                    this.showError('senha', 'Senha deve ter pelo menos 6 caracteres');
                    return false;
                }
                
                this.clearError('senha');
                return true;
            }

            showError(field, message) {
                const input = document.getElementById(field);
                const errorElement = document.getElementById(field + 'Error');
                
                input.classList.add('error');
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }

            clearError(field) {
                const input = document.getElementById(field);
                const errorElement = document.getElementById(field + 'Error');
                
                input.classList.remove('error');
                errorElement.style.display = 'none';
            }

            setLoading(loading) {
                if (loading) {
                    this.loginButton.disabled = true;
                    this.loginButton.style.display = 'none';
                    this.loading.style.display = 'block';
                } else {
                    this.loginButton.disabled = false;
                    this.loginButton.style.display = 'block';
                    this.loading.style.display = 'none';
                }
            }

            isLoggedIn() {
                return localStorage.getItem('user') && localStorage.getItem('token');
            }
        }

        // Inicializar sistema de login
        document.addEventListener('DOMContentLoaded', () => {
            new LoginSystem();
        });
    </script>
</body>
</html>
