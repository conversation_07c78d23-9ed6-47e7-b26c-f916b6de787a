<?php
/**
 * Roteador Principal da API
 * Sistema Saúde Flex - Agendamentos
 */

require_once __DIR__ . '/config/config.php';

// Obter a rota da URL
$request_uri = $_SERVER['REQUEST_URI'];
$script_name = $_SERVER['SCRIPT_NAME'];

// Remover o caminho do script da URI
$path = str_replace(dirname($script_name), '', $request_uri);
$path = trim($path, '/');

// Remover query string
$path = strtok($path, '?');

// Dividir o caminho em segmentos
$segments = explode('/', $path);

// Remover 'api' se estiver presente
if (isset($segments[0]) && $segments[0] === 'api') {
    array_shift($segments);
}

// Obter o módulo (primeiro segmento)
$module = $segments[0] ?? '';

// Obter o path adicional (demais segmentos)
$additionalPath = isset($segments[1]) ? implode('/', array_slice($segments, 1)) : '';

// Definir o path no $_GET para os endpoints
if ($additionalPath) {
    $_GET['path'] = $additionalPath;
}

// Verificar se há ID na URL
if (isset($segments[1]) && is_numeric($segments[1])) {
    $_GET['id'] = $segments[1];
    if (isset($segments[2])) {
        $_GET['path'] = $segments[2];
    }
}

// Roteamento
switch ($module) {
    case 'auth':
        require_once __DIR__ . '/endpoints/auth.php';
        break;
        
    case 'usuarios':
        require_once __DIR__ . '/endpoints/usuarios.php';
        break;
        
    case 'clientes':
        require_once __DIR__ . '/endpoints/clientes.php';
        break;
        
    case 'produtos':
        require_once __DIR__ . '/endpoints/produtos.php';
        break;
        
    case 'funcionarios':
        require_once __DIR__ . '/endpoints/funcionarios.php';
        break;
        
    case 'agendamentos':
        require_once __DIR__ . '/endpoints/agendamentos.php';
        break;
        
    case 'vendas':
        require_once __DIR__ . '/endpoints/vendas.php';
        break;
        
    case 'dashboard':
        require_once __DIR__ . '/endpoints/dashboard.php';
        break;
        
    case 'relatorios':
        require_once __DIR__ . '/endpoints/relatorios.php';
        break;
        
    case '':
        // Rota raiz - informações da API
        jsonResponse([
            'name' => SYSTEM_NAME,
            'version' => SYSTEM_VERSION,
            'status' => 'online',
            'timestamp' => date('Y-m-d H:i:s'),
            'endpoints' => [
                'auth' => [
                    'POST /api/auth/login' => 'Login do usuário',
                    'POST /api/auth/logout' => 'Logout do usuário',
                    'GET /api/auth/validate' => 'Validar token',
                    'GET /api/auth/me' => 'Dados do usuário atual'
                ],
                'usuarios' => [
                    'GET /api/usuarios' => 'Listar usuários',
                    'POST /api/usuarios' => 'Criar usuário',
                    'PUT /api/usuarios/{id}' => 'Atualizar usuário',
                    'DELETE /api/usuarios/{id}' => 'Excluir usuário'
                ],
                'clientes' => [
                    'GET /api/clientes' => 'Listar clientes',
                    'GET /api/clientes/{id}' => 'Buscar cliente',
                    'POST /api/clientes' => 'Criar cliente',
                    'PUT /api/clientes/{id}' => 'Atualizar cliente',
                    'DELETE /api/clientes/{id}' => 'Excluir cliente',
                    'GET /api/clientes/search?q={termo}' => 'Pesquisar clientes'
                ],
                'produtos' => [
                    'GET /api/produtos' => 'Listar produtos',
                    'GET /api/produtos/{id}' => 'Buscar produto',
                    'POST /api/produtos' => 'Criar produto',
                    'PUT /api/produtos/{id}' => 'Atualizar produto',
                    'DELETE /api/produtos/{id}' => 'Excluir produto'
                ],
                'funcionarios' => [
                    'GET /api/funcionarios' => 'Listar funcionários',
                    'GET /api/funcionarios/{id}' => 'Buscar funcionário',
                    'POST /api/funcionarios' => 'Criar funcionário',
                    'PUT /api/funcionarios/{id}' => 'Atualizar funcionário',
                    'DELETE /api/funcionarios/{id}' => 'Excluir funcionário'
                ],
                'agendamentos' => [
                    'GET /api/agendamentos' => 'Listar agendamentos',
                    'GET /api/agendamentos/{id}' => 'Buscar agendamento',
                    'POST /api/agendamentos' => 'Criar agendamento',
                    'PUT /api/agendamentos/{id}' => 'Atualizar agendamento',
                    'DELETE /api/agendamentos/{id}' => 'Excluir agendamento'
                ],
                'dashboard' => [
                    'GET /api/dashboard/stats' => 'Estatísticas do dashboard'
                ]
            ]
        ]);
        break;
        
    default:
        jsonResponse(['error' => 'Endpoint não encontrado'], 404);
}
