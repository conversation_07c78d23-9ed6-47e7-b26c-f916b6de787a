<!-- <PERSON><PERSON><PERSON><PERSON> de Usuários -->
<div class="page-header">
    <div>
        <h1 class="page-title">Usuários</h1>
        <p class="page-subtitle">Gerencie usuários e permissões do sistema</p>
    </div>
    <div>
        <button class="btn btn-primary" onclick="openModal('novoUsuarioModal')">
            <i class="fas fa-plus"></i>
            Novo Usuário
        </button>
    </div>
</div>

<!-- Filtros -->
<div class="filters-container">
    <div class="filters-row">
        <div class="filter-group">
            <label class="filter-label">Buscar</label>
            <input type="text" class="filter-input" id="filtroBuscaUsuario" placeholder="Nome ou email...">
        </div>
        <div class="filter-group">
            <label class="filter-label">Tipo</label>
            <select class="filter-input" id="filtroTipoUsuario">
                <option value="">Todos os tipos</option>
                <option value="admin">Administrador</option>
                <option value="gerente">Gerente</option>
                <option value="vendedor">Vendedor</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Status</label>
            <select class="filter-input" id="filtroStatusUsuario">
                <option value="">Todos os status</option>
                <option value="ativo">Ativo</option>
                <option value="inativo">Inativo</option>
            </select>
        </div>
        <div class="filter-group">
            <button class="btn btn-primary" onclick="aplicarFiltrosUsuarios()">
                <i class="fas fa-search"></i>
                Filtrar
            </button>
            <button class="btn btn-secondary" onclick="limparFiltrosUsuarios()">
                <i class="fas fa-times"></i>
                Limpar
            </button>
        </div>
    </div>
</div>

<!-- Lista de Usuários -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Lista de Usuários</h3>
        <div style="display: flex; gap: 1rem; align-items: center;">
            <span id="contadorUsuarios" style="color: var(--gray-600); font-size: 0.875rem;">0 usuários</span>
        </div>
    </div>
    <div class="card-body">
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>Nome</th>
                        <th>Email</th>
                        <th>Tipo</th>
                        <th>Status</th>
                        <th>Último Login</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody id="usuariosTableBody">
                    <!-- Dados serão carregados dinamicamente -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Novo/Editar Usuário -->
<div id="novoUsuarioModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">Novo Usuário</h2>
            <button class="modal-close" onclick="closeModal('novoUsuarioModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="novoUsuarioForm" onsubmit="handleSalvarUsuario(event)">
            <div class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="usuarioNome">Nome *</label>
                        <input type="text" id="usuarioNome" name="nome" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="usuarioEmail">Email *</label>
                        <input type="email" id="usuarioEmail" name="email" class="form-control" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="usuarioTipo">Tipo de Usuário *</label>
                        <select id="usuarioTipo" name="tipo" class="form-control" required>
                            <option value="">Selecione o tipo</option>
                            <option value="admin">Administrador</option>
                            <option value="gerente">Gerente</option>
                            <option value="vendedor">Vendedor</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="usuarioStatus">Status</label>
                        <select id="usuarioStatus" name="status" class="form-control">
                            <option value="ativo">Ativo</option>
                            <option value="inativo">Inativo</option>
                        </select>
                    </div>
                </div>

                <div class="form-row" id="senhaFields">
                    <div class="form-group">
                        <label class="form-label" for="usuarioSenha">Senha *</label>
                        <input type="password" id="usuarioSenha" name="senha" class="form-control" minlength="6">
                        <small class="form-text">Mínimo 6 caracteres</small>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="usuarioConfirmarSenha">Confirmar Senha *</label>
                        <input type="password" id="usuarioConfirmarSenha" name="confirmarSenha" class="form-control" minlength="6">
                    </div>
                </div>

                <!-- Informações sobre permissões -->
                <div class="alert alert-info">
                    <h4><i class="fas fa-info-circle"></i> Permissões por Tipo:</h4>
                    <ul style="margin: 0.5rem 0 0 1rem;">
                        <li><strong>Administrador:</strong> Acesso total ao sistema</li>
                        <li><strong>Gerente:</strong> Gerencia produtos, vendas, agendamentos e clientes</li>
                        <li><strong>Vendedor:</strong> Apenas vendas, agendamentos e cadastro de clientes</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('novoUsuarioModal')">
                    Cancelar
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Salvar
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Variáveis globais da página
let usuariosFiltrados = [];

// Carregar usuários
async function carregarUsuarios() {
    try {
        const response = await fetch('/api/usuarios', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (response.ok) {
            const usuarios = await response.json();
            app.data.usuarios = usuarios;
            usuariosFiltrados = [...usuarios];
            renderizarTabelaUsuarios();
        } else {
            notifications.error('Erro ao carregar usuários');
        }
    } catch (error) {
        console.error('Erro ao carregar usuários:', error);
        notifications.error('Erro de conexão ao carregar usuários');
    }
}

// Renderizar tabela
function renderizarTabelaUsuarios() {
    const tbody = document.getElementById('usuariosTableBody');
    if (!tbody) return;

    if (usuariosFiltrados.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center" style="padding: 2rem; color: var(--gray-500);">
                    Nenhum usuário encontrado
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = usuariosFiltrados.map(usuario => `
        <tr>
            <td>
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <div style="font-weight: 600;">${usuario.nome}</div>
                    </div>
                </div>
            </td>
            <td>${usuario.email}</td>
            <td>
                <span class="badge badge-${getTipoBadgeClass(usuario.tipo)}">
                    ${getTipoLabel(usuario.tipo)}
                </span>
            </td>
            <td>
                <span class="badge badge-${usuario.status === 'ativo' ? 'success' : 'error'}">
                    ${usuario.status === 'ativo' ? 'Ativo' : 'Inativo'}
                </span>
            </td>
            <td>
                ${usuario.ultimo_login ? formatters.datetime(usuario.ultimo_login) : 'Nunca'}
            </td>
            <td>
                <div class="dropdown">
                    <button class="btn btn-sm btn-secondary dropdown-toggle">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="dropdown-menu">
                        <div class="dropdown-item" onclick="editarUsuario(${usuario.id})">
                            <i class="fas fa-edit"></i> Editar
                        </div>
                        ${usuario.status === 'ativo' ? 
                            `<div class="dropdown-item" onclick="alterarStatusUsuario(${usuario.id}, 'inativo')">
                                <i class="fas fa-ban"></i> Desativar
                            </div>` :
                            `<div class="dropdown-item" onclick="alterarStatusUsuario(${usuario.id}, 'ativo')">
                                <i class="fas fa-check"></i> Ativar
                            </div>`
                        }
                        <div class="dropdown-item" onclick="resetarSenhaUsuario(${usuario.id})">
                            <i class="fas fa-key"></i> Resetar Senha
                        </div>
                        ${usuario.id !== getCurrentUserId() ? 
                            `<div class="dropdown-item" onclick="excluirUsuario(${usuario.id})">
                                <i class="fas fa-trash"></i> Excluir
                            </div>` : ''
                        }
                    </div>
                </div>
            </td>
        </tr>
    `).join('');

    // Configurar dropdowns
    configurarDropdowns();
    
    // Atualizar contador
    const contador = document.getElementById('contadorUsuarios');
    if (contador) {
        contador.textContent = `${usuariosFiltrados.length} usuário${usuariosFiltrados.length !== 1 ? 's' : ''}`;
    }
}

// Aplicar filtros
function aplicarFiltrosUsuarios() {
    const busca = document.getElementById('filtroBuscaUsuario').value.toLowerCase();
    const tipo = document.getElementById('filtroTipoUsuario').value;
    const status = document.getElementById('filtroStatusUsuario').value;

    usuariosFiltrados = app.data.usuarios.filter(usuario => {
        if (busca && !usuario.nome.toLowerCase().includes(busca) && 
            !usuario.email.toLowerCase().includes(busca)) {
            return false;
        }
        
        if (tipo && usuario.tipo !== tipo) return false;
        if (status && usuario.status !== status) return false;
        
        return true;
    });

    renderizarTabelaUsuarios();
}

// Limpar filtros
function limparFiltrosUsuarios() {
    document.getElementById('filtroBuscaUsuario').value = '';
    document.getElementById('filtroTipoUsuario').value = '';
    document.getElementById('filtroStatusUsuario').value = '';
    
    usuariosFiltrados = [...app.data.usuarios];
    renderizarTabelaUsuarios();
}

// Salvar usuário
async function handleSalvarUsuario(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const isEditing = form.dataset.editingId;
    
    // Validar senhas
    if (!isEditing || formData.get('senha')) {
        const senha = formData.get('senha');
        const confirmarSenha = formData.get('confirmarSenha');
        
        if (senha !== confirmarSenha) {
            notifications.error('As senhas não coincidem');
            return;
        }
        
        if (senha.length < 6) {
            notifications.error('A senha deve ter pelo menos 6 caracteres');
            return;
        }
    }

    const usuario = {
        nome: formData.get('nome'),
        email: formData.get('email'),
        tipo: formData.get('tipo'),
        status: formData.get('status')
    };

    // Adicionar senha apenas se foi preenchida
    if (formData.get('senha')) {
        usuario.senha = formData.get('senha');
    }

    try {
        let response;
        if (isEditing) {
            response = await fetch(`/api/usuarios/${isEditing}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(usuario)
            });
        } else {
            response = await fetch('/api/usuarios', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(usuario)
            });
        }

        if (response.ok) {
            notifications.success(isEditing ? 'Usuário atualizado com sucesso!' : 'Usuário criado com sucesso!');
            closeModal('novoUsuarioModal');
            form.reset();
            delete form.dataset.editingId;
            carregarUsuarios();
        } else {
            const error = await response.json();
            notifications.error(error.error || 'Erro ao salvar usuário');
        }
    } catch (error) {
        console.error('Erro ao salvar usuário:', error);
        notifications.error('Erro de conexão ao salvar usuário');
    }
}

// Funções auxiliares
function getTipoBadgeClass(tipo) {
    const tipoMap = {
        'admin': 'error',
        'gerente': 'warning',
        'vendedor': 'info'
    };
    return tipoMap[tipo] || 'info';
}

function getTipoLabel(tipo) {
    const tipoMap = {
        'admin': 'Administrador',
        'gerente': 'Gerente',
        'vendedor': 'Vendedor'
    };
    return tipoMap[tipo] || tipo;
}

function getCurrentUserId() {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    return user.id;
}

// Funções auxiliares que estavam faltando
function editarUsuario(id) {
    const usuario = app.data.usuarios.find(u => u.id === id);
    if (!usuario) return;

    const form = document.getElementById('novoUsuarioForm');

    // Preencher formulário
    form.querySelector('[name="nome"]').value = usuario.nome || '';
    form.querySelector('[name="email"]').value = usuario.email || '';
    form.querySelector('[name="tipo"]').value = usuario.tipo || '';
    form.querySelector('[name="status"]').value = usuario.status || 'ativo';

    // Tornar senha opcional na edição
    const senhaFields = document.getElementById('senhaFields');
    const senhaInputs = senhaFields.querySelectorAll('input');
    senhaInputs.forEach(input => {
        input.required = false;
        input.placeholder = 'Deixe em branco para manter a senha atual';
    });

    // Marcar como edição
    form.dataset.editingId = id;

    // Alterar título do modal
    document.querySelector('#novoUsuarioModal .modal-title').textContent = 'Editar Usuário';

    openModal('novoUsuarioModal');
}

function alterarStatusUsuario(id, novoStatus) {
    if (confirm(`Tem certeza que deseja ${novoStatus === 'ativo' ? 'ativar' : 'desativar'} este usuário?`)) {
        fetch(`/api/usuarios/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({ status: novoStatus })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                notifications.error(data.error);
            } else {
                notifications.success(`Usuário ${novoStatus === 'ativo' ? 'ativado' : 'desativado'} com sucesso!`);
                carregarUsuarios();
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            notifications.error('Erro ao alterar status do usuário');
        });
    }
}

function resetarSenhaUsuario(id) {
    const novaSenha = prompt('Digite a nova senha (mínimo 6 caracteres):');
    if (novaSenha && novaSenha.length >= 6) {
        fetch(`/api/usuarios/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({ senha: novaSenha })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                notifications.error(data.error);
            } else {
                notifications.success('Senha resetada com sucesso!');
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            notifications.error('Erro ao resetar senha');
        });
    } else if (novaSenha !== null) {
        notifications.error('A senha deve ter pelo menos 6 caracteres');
    }
}

function excluirUsuario(id) {
    if (confirm('Tem certeza que deseja excluir este usuário? Esta ação não pode ser desfeita.')) {
        fetch(`/api/usuarios/${id}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                notifications.error(data.error);
            } else {
                notifications.success('Usuário excluído com sucesso!');
                carregarUsuarios();
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            notifications.error('Erro ao excluir usuário');
        });
    }
}

function carregarListaUsuarios() {
    carregarUsuarios();
}

// Configurar eventos quando a página carregar
document.addEventListener('DOMContentLoaded', function() {
    // Configurar filtros
    const filtros = ['filtroBuscaUsuario', 'filtroTipoUsuario', 'filtroStatusUsuario'];
    filtros.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            if (element.type === 'text') {
                element.addEventListener('input', debounce(aplicarFiltrosUsuarios, 300));
            } else {
                element.addEventListener('change', aplicarFiltrosUsuarios);
            }
        }
    });

    // Carregar usuários se os dados já estiverem disponíveis
    if (typeof app !== 'undefined' && app.data && app.data.usuarios) {
        usuariosFiltrados = [...app.data.usuarios];
        renderizarTabelaUsuarios();
    }
});
</script>
