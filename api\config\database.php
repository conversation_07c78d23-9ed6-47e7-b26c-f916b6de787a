<?php
/**
 * Configuração do Banco de Dados MariaDB/MySQL
 * Sistema Saúde Flex - Agendamentos
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'saude_flex';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    public $pdo;

    public function __construct() {
        $this->connect();
    }

    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->db_name};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch (PDOException $e) {
            // Se não conseguir conectar, tentar criar o banco
            $this->createDatabase();
        }
    }

    private function createDatabase() {
        try {
            $dsn = "mysql:host={$this->host};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $pdo = new PDO($dsn, $this->username, $this->password, $options);
            $pdo->exec("CREATE DATABASE IF NOT EXISTS {$this->db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            // Reconectar com o banco criado
            $dsn = "mysql:host={$this->host};dbname={$this->db_name};charset={$this->charset}";
            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
            
            // Criar tabelas
            $this->createTables();
            $this->insertInitialData();
            
        } catch (PDOException $e) {
            die("Erro na conexão com o banco de dados: " . $e->getMessage());
        }
    }

    private function createTables() {
        $tables = [
            // Tabela de usuários
            "CREATE TABLE IF NOT EXISTS usuarios (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nome VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                senha VARCHAR(255) NOT NULL,
                tipo ENUM('admin', 'gerente', 'vendedor') NOT NULL,
                status ENUM('ativo', 'inativo') DEFAULT 'ativo',
                ultimo_login TIMESTAMP NULL,
                data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // Tabela de clientes
            "CREATE TABLE IF NOT EXISTS clientes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nome VARCHAR(255) NOT NULL,
                email VARCHAR(255),
                telefone VARCHAR(20),
                endereco TEXT,
                cidade VARCHAR(100),
                estado VARCHAR(2),
                cep VARCHAR(10),
                data_nascimento DATE,
                genero ENUM('masculino', 'feminino', 'outro'),
                observacoes TEXT,
                status ENUM('ativo', 'inativo') DEFAULT 'ativo',
                data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // Tabela de produtos/serviços
            "CREATE TABLE IF NOT EXISTS produtos (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nome VARCHAR(255) NOT NULL,
                categoria VARCHAR(100),
                descricao TEXT,
                preco_custo DECIMAL(10,2) DEFAULT 0.00,
                lucro_desejado DECIMAL(10,2) DEFAULT 0.00,
                tipo_lucro ENUM('valor', 'percentual') DEFAULT 'percentual',
                preco_venda DECIMAL(10,2) DEFAULT 0.00,
                estoque_atual INT DEFAULT 0,
                estoque_minimo INT DEFAULT 0,
                duracao_estimada INT COMMENT 'Duração em minutos',
                foto VARCHAR(500),
                status ENUM('ativo', 'inativo') DEFAULT 'ativo',
                data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // Tabela de funcionários
            "CREATE TABLE IF NOT EXISTS funcionarios (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nome VARCHAR(255) NOT NULL,
                email VARCHAR(255),
                telefone VARCHAR(20),
                cargo VARCHAR(100),
                especialidades JSON,
                status ENUM('ativo', 'inativo') DEFAULT 'ativo',
                data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // Tabela de agendamentos
            "CREATE TABLE IF NOT EXISTS agendamentos (
                id INT AUTO_INCREMENT PRIMARY KEY,
                cliente_id INT NOT NULL,
                funcionario_id INT NOT NULL,
                produto_id INT,
                data_visita DATE NOT NULL,
                hora_inicio TIME NOT NULL,
                hora_fim TIME NOT NULL,
                endereco_visita TEXT,
                observacoes TEXT,
                valor DECIMAL(10,2),
                status ENUM('agendado', 'confirmado', 'concluido', 'cancelado') DEFAULT 'agendado',
                data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (cliente_id) REFERENCES clientes(id) ON DELETE CASCADE,
                FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE,
                FOREIGN KEY (produto_id) REFERENCES produtos(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // Tabela de vendas
            "CREATE TABLE IF NOT EXISTS vendas (
                id INT AUTO_INCREMENT PRIMARY KEY,
                cliente_id INT NOT NULL,
                vendedor_id INT NOT NULL,
                subtotal DECIMAL(10,2) NOT NULL,
                desconto_total DECIMAL(10,2) DEFAULT 0.00,
                total DECIMAL(10,2) NOT NULL,
                data_venda TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                observacoes TEXT,
                FOREIGN KEY (cliente_id) REFERENCES clientes(id) ON DELETE CASCADE,
                FOREIGN KEY (vendedor_id) REFERENCES usuarios(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // Tabela de itens da venda
            "CREATE TABLE IF NOT EXISTS itens_venda (
                id INT AUTO_INCREMENT PRIMARY KEY,
                venda_id INT NOT NULL,
                produto_id INT NOT NULL,
                quantidade INT NOT NULL,
                preco_unitario DECIMAL(10,2) NOT NULL,
                desconto DECIMAL(10,2) DEFAULT 0.00,
                subtotal DECIMAL(10,2) NOT NULL,
                FOREIGN KEY (venda_id) REFERENCES vendas(id) ON DELETE CASCADE,
                FOREIGN KEY (produto_id) REFERENCES produtos(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // Tabela de logs de auditoria
            "CREATE TABLE IF NOT EXISTS logs_auditoria (
                id INT AUTO_INCREMENT PRIMARY KEY,
                usuario_id INT,
                acao VARCHAR(100) NOT NULL,
                tabela VARCHAR(50) NOT NULL,
                registro_id INT,
                dados_anteriores JSON,
                dados_novos JSON,
                data_acao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
        ];

        foreach ($tables as $sql) {
            $this->pdo->exec($sql);
        }
    }

    private function insertInitialData() {
        // Verificar se já existe usuário admin
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM usuarios WHERE tipo = 'admin'");
        $stmt->execute();
        $adminExists = $stmt->fetchColumn() > 0;

        if (!$adminExists) {
            // Criar usuário administrador padrão
            $senhaAdmin = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $this->pdo->prepare("
                INSERT INTO usuarios (nome, email, senha, tipo) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute(['Administrador', '<EMAIL>', $senhaAdmin, 'admin']);

            // Inserir produtos iniciais
            $produtos = [
                ['Consulta Nutricional', 'Nutrição', 'Avaliação nutricional completa com plano alimentar personalizado', 50.00, 50.00, 'valor', 100.00, 10, 5, 60],
                ['Suplementos Vitamínicos', 'Suplementação', 'Linha completa de vitaminas e minerais', 30.00, 59.90, 'valor', 89.90, 50, 10, 30],
                ['Programa Detox', 'Bem-estar', 'Programa completo de desintoxicação de 21 dias', 150.00, 149.00, 'valor', 299.00, 20, 5, 45],
                ['Avaliação Física', 'Fitness', 'Avaliação corporal e plano de exercícios', 40.00, 80.00, 'valor', 120.00, 15, 3, 90],
                ['Produtos Naturais', 'Fitoterapia', 'Linha de produtos naturais e fitoterápicos', 25.00, 40.00, 'valor', 65.00, 30, 8, 30]
            ];

            $stmt = $this->pdo->prepare("
                INSERT INTO produtos (nome, categoria, descricao, preco_custo, lucro_desejado, tipo_lucro, preco_venda, estoque_atual, estoque_minimo, duracao_estimada) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            foreach ($produtos as $produto) {
                $stmt->execute($produto);
            }

            // Inserir funcionários iniciais
            $funcionarios = [
                ['Ana Silva', '<EMAIL>', '(11) 99999-1111', 'Consultora Sênior', '["Nutrição", "Suplementação"]'],
                ['Carlos Santos', '<EMAIL>', '(11) 99999-2222', 'Consultor', '["Fitness", "Bem-estar"]'],
                ['Maria Oliveira', '<EMAIL>', '(11) 99999-3333', 'Especialista', '["Fitoterapia", "Produtos Naturais"]']
            ];

            $stmt = $this->pdo->prepare("
                INSERT INTO funcionarios (nome, email, telefone, cargo, especialidades) 
                VALUES (?, ?, ?, ?, ?)
            ");

            foreach ($funcionarios as $funcionario) {
                $stmt->execute($funcionario);
            }
        }
    }

    public function getConnection() {
        return $this->pdo;
    }
}
