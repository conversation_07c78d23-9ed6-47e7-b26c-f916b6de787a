<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Navegação</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .nav-test {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .nav-test button {
            background: #28a745;
        }
        .nav-test button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Debug - Sistema de Navegação</h1>
        
        <div class="test-section">
            <h3>1. Verificação de Elementos</h3>
            <button onclick="checkElements()">Verificar Elementos</button>
            <div id="elements-status" class="status info">Aguardando verificação...</div>
            <div id="elements-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>2. Teste de Navegação</h3>
            <div class="nav-test">
                <button onclick="testNavigation('dashboard')">Dashboard</button>
                <button onclick="testNavigation('agendamentos')">Agendamentos</button>
                <button onclick="testNavigation('clientes')">Clientes</button>
                <button onclick="testNavigation('produtos')">Produtos</button>
                <button onclick="testNavigation('funcionarios')">Funcionários</button>
                <button onclick="testNavigation('relatorios')">Relatórios</button>
                <button onclick="testNavigation('usuarios')">Usuários</button>
                <button onclick="testNavigation('configuracoes')">Configurações</button>
            </div>
            <div id="navigation-status" class="status info">Aguardando teste...</div>
            <div id="navigation-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>3. Verificação de Scripts</h3>
            <button onclick="checkScripts()">Verificar Scripts</button>
            <div id="scripts-status" class="status info">Aguardando verificação...</div>
            <div id="scripts-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>4. Teste de Autenticação</h3>
            <button onclick="checkAuth()">Verificar Autenticação</button>
            <button onclick="simulateLogin()">Simular Login</button>
            <div id="auth-status" class="status info">Aguardando verificação...</div>
            <div id="auth-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>5. Console do Navegador</h3>
            <p>Abra o console do navegador (F12) para ver logs detalhados.</p>
            <button onclick="showConsoleInfo()">Mostrar Info do Console</button>
            <div id="console-log" class="log"></div>
        </div>
    </div>

    <script>
        let logs = {
            elements: '',
            navigation: '',
            scripts: '',
            auth: '',
            console: ''
        };

        function updateStatus(section, status, message) {
            const element = document.getElementById(`${section}-status`);
            element.className = `status ${status}`;
            element.textContent = message;
        }

        function addLog(section, message) {
            logs[section] += new Date().toLocaleTimeString() + ': ' + message + '\n';
            document.getElementById(`${section}-log`).textContent = logs[section];
        }

        function checkElements() {
            updateStatus('elements', 'info', 'Verificando elementos...');
            addLog('elements', 'Iniciando verificação de elementos');

            // Verificar se os elementos existem
            const checks = [
                { name: 'Sidebar', selector: '.sidebar' },
                { name: 'Nav Links', selector: '.nav-link' },
                { name: 'Pages', selector: '.page' },
                { name: 'Dashboard Page', selector: '#dashboard-page' },
                { name: 'Agendamentos Page', selector: '#agendamentos-page' },
                { name: 'Clientes Page', selector: '#clientes-page' },
                { name: 'Produtos Page', selector: '#produtos-page' }
            ];

            let allFound = true;
            checks.forEach(check => {
                const elements = document.querySelectorAll(check.selector);
                if (elements.length > 0) {
                    addLog('elements', `✓ ${check.name}: ${elements.length} elemento(s) encontrado(s)`);
                } else {
                    addLog('elements', `✗ ${check.name}: Não encontrado`);
                    allFound = false;
                }
            });

            if (allFound) {
                updateStatus('elements', 'success', 'Todos os elementos encontrados');
            } else {
                updateStatus('elements', 'error', 'Alguns elementos não foram encontrados');
            }
        }

        function testNavigation(page) {
            updateStatus('navigation', 'info', `Testando navegação para ${page}...`);
            addLog('navigation', `Tentando navegar para: ${page}`);

            try {
                // Verificar se o app existe
                if (typeof window.app === 'undefined') {
                    addLog('navigation', '✗ Objeto app não encontrado');
                    updateStatus('navigation', 'error', 'Objeto app não encontrado');
                    return;
                }

                // Verificar se a função navigateTo existe
                if (typeof window.app.navigateTo !== 'function') {
                    addLog('navigation', '✗ Função navigateTo não encontrada');
                    updateStatus('navigation', 'error', 'Função navigateTo não encontrada');
                    return;
                }

                // Tentar navegar
                window.app.navigateTo(page);
                addLog('navigation', `✓ Navegação para ${page} executada`);
                
                // Verificar se a página está ativa
                setTimeout(() => {
                    const activePage = document.querySelector('.page.active');
                    if (activePage && activePage.id === `${page}-page`) {
                        addLog('navigation', `✓ Página ${page} está ativa`);
                        updateStatus('navigation', 'success', `Navegação para ${page} bem-sucedida`);
                    } else {
                        addLog('navigation', `✗ Página ${page} não está ativa`);
                        updateStatus('navigation', 'error', `Navegação para ${page} falhou`);
                    }
                }, 100);

            } catch (error) {
                addLog('navigation', `✗ Erro: ${error.message}`);
                updateStatus('navigation', 'error', `Erro na navegação: ${error.message}`);
            }
        }

        function checkScripts() {
            updateStatus('scripts', 'info', 'Verificando scripts...');
            addLog('scripts', 'Verificando scripts carregados');

            const scripts = [
                { name: 'utils.js', check: () => typeof notifications !== 'undefined' },
                { name: 'api.js', check: () => typeof api !== 'undefined' },
                { name: 'calendar.js', check: () => typeof Calendar !== 'undefined' },
                { name: 'app.js', check: () => typeof window.app !== 'undefined' }
            ];

            let allLoaded = true;
            scripts.forEach(script => {
                try {
                    if (script.check()) {
                        addLog('scripts', `✓ ${script.name}: Carregado`);
                    } else {
                        addLog('scripts', `✗ ${script.name}: Não carregado`);
                        allLoaded = false;
                    }
                } catch (error) {
                    addLog('scripts', `✗ ${script.name}: Erro - ${error.message}`);
                    allLoaded = false;
                }
            });

            if (allLoaded) {
                updateStatus('scripts', 'success', 'Todos os scripts carregados');
            } else {
                updateStatus('scripts', 'error', 'Alguns scripts não foram carregados');
            }
        }

        function checkAuth() {
            updateStatus('auth', 'info', 'Verificando autenticação...');
            addLog('auth', 'Verificando estado de autenticação');

            const user = localStorage.getItem('user');
            const token = localStorage.getItem('token');

            if (user && token) {
                try {
                    const userData = JSON.parse(user);
                    addLog('auth', `✓ Usuário logado: ${userData.nome || userData.email}`);
                    addLog('auth', `✓ Token presente: ${token.substring(0, 20)}...`);
                    updateStatus('auth', 'success', 'Usuário autenticado');
                } catch (error) {
                    addLog('auth', `✗ Erro ao parsear dados do usuário: ${error.message}`);
                    updateStatus('auth', 'error', 'Dados de usuário corrompidos');
                }
            } else {
                addLog('auth', '✗ Usuário não autenticado');
                updateStatus('auth', 'error', 'Usuário não autenticado');
            }
        }

        function simulateLogin() {
            addLog('auth', 'Simulando login...');
            
            const userData = {
                id: 1,
                nome: 'Administrador',
                email: '<EMAIL>',
                tipo: 'admin'
            };
            
            const token = 'fake-jwt-token-for-testing';
            
            localStorage.setItem('user', JSON.stringify(userData));
            localStorage.setItem('token', token);
            
            addLog('auth', '✓ Login simulado com sucesso');
            updateStatus('auth', 'success', 'Login simulado');
            
            // Recarregar a página para aplicar a autenticação
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }

        function showConsoleInfo() {
            addLog('console', 'Informações do console:');
            addLog('console', `User Agent: ${navigator.userAgent}`);
            addLog('console', `URL: ${window.location.href}`);
            addLog('console', `Largura da tela: ${window.innerWidth}px`);
            addLog('console', `Altura da tela: ${window.innerHeight}px`);
            
            // Verificar erros no console
            const originalError = console.error;
            const errors = [];
            console.error = function(...args) {
                errors.push(args.join(' '));
                originalError.apply(console, args);
            };
            
            if (errors.length > 0) {
                addLog('console', `Erros encontrados: ${errors.length}`);
                errors.forEach(error => addLog('console', `- ${error}`));
            } else {
                addLog('console', 'Nenhum erro encontrado no console');
            }
        }

        // Executar verificações automáticas quando a página carregar
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkElements();
                checkScripts();
                checkAuth();
            }, 1000);
        });
    </script>
</body>
</html>
