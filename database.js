// Configuração do banco de dados SQLite WASM para navegador
class DatabaseManager {
    constructor() {
        this.db = null;
        this.isInitialized = false;
        this.sqlite3 = null;
    }

    async initialize() {
        try {
            console.log('Inicializando SQLite WASM...');

            // Carregar SQLite WASM
            const sqliteModule = await import('https://cdn.jsdelivr.net/npm/@sqlite.org/sqlite-wasm@3.44.2/sqlite-wasm.mjs');
            this.sqlite3 = await sqliteModule.default();

            // Criar banco de dados em memória
            this.db = new this.sqlite3.oo1.DB(':memory:');

            await this.createTables();
            await this.insertDefaultData();

            this.isInitialized = true;
            console.log('Banco de dados SQLite WASM inicializado com sucesso!');

            return true;
        } catch (error) {
            console.error('Erro ao inicializar banco de dados:', error);
            // Fallback para localStorage se SQLite WASM falhar
            console.log('Usando localStorage como fallback...');
            this.initializeLocalStorage();
            return true;
        }
    }

    initializeLocalStorage() {
        // Fallback usando localStorage
        this.isInitialized = true;
        this.db = {
            exec: (sql) => console.log('LocalStorage exec:', sql),
            selectObjects: (sql) => this.getFromLocalStorage(sql),
            selectObject: (sql) => this.getFromLocalStorage(sql)[0] || null,
            selectValue: (sql) => {
                const result = this.getFromLocalStorage(sql);
                return result.length > 0 ? Object.values(result[0])[0] : null;
            }
        };

        // Inicializar dados padrão no localStorage
        this.initializeLocalStorageData();
    }

    initializeLocalStorageData() {
        // Verificar se já existe usuário admin
        const usuarios = JSON.parse(localStorage.getItem('usuarios') || '[]');
        const adminExists = usuarios.some(u => u.tipo === 'admin');

        if (!adminExists) {
            // Criar usuário admin
            const adminUser = {
                id: 1,
                nome: 'Administrador',
                email: '<EMAIL>',
                senha: this.hashPasswordSimple('admin123'),
                tipo: 'admin',
                ativo: true,
                data_criacao: new Date().toISOString()
            };
            usuarios.push(adminUser);
            localStorage.setItem('usuarios', JSON.stringify(usuarios));

            console.log('Usuário admin criado com sucesso!');
            console.log('Email: <EMAIL>');
            console.log('Senha: admin123');
        }

        // Inserir dados de exemplo se não existirem
        this.insertSampleDataLocalStorage();
    }

    hashPasswordSimple(password) {
        // Hash simples para demonstração (use bcrypt.js em produção)
        return btoa(password + 'salt');
    }

    getFromLocalStorage(sql) {
        // Parser simples para SQL SELECT (apenas para demonstração)
        if (sql.includes('usuarios')) {
            return JSON.parse(localStorage.getItem('usuarios') || '[]');
        } else if (sql.includes('clientes')) {
            return JSON.parse(localStorage.getItem('clientes') || '[]');
        } else if (sql.includes('produtos')) {
            return JSON.parse(localStorage.getItem('produtos') || '[]');
        } else if (sql.includes('agendamentos')) {
            return JSON.parse(localStorage.getItem('agendamentos') || '[]');
        }
        return [];
    }

    async createTables() {
        const tables = [
            // Tabela de usuários
            `CREATE TABLE IF NOT EXISTS usuarios (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nome TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL,
                senha TEXT NOT NULL,
                tipo TEXT NOT NULL CHECK(tipo IN ('admin', 'gerente', 'vendedor')),
                ativo BOOLEAN DEFAULT 1,
                data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
                data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Tabela de clientes
            `CREATE TABLE IF NOT EXISTS clientes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nome TEXT NOT NULL,
                telefone TEXT,
                email TEXT,
                endereco TEXT,
                numero TEXT,
                bairro TEXT,
                cidade TEXT,
                uf TEXT,
                cep TEXT,
                observacoes TEXT,
                data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
                data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Tabela de produtos/serviços
            `CREATE TABLE IF NOT EXISTS produtos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nome TEXT NOT NULL,
                descricao TEXT,
                preco_custo DECIMAL(10,2),
                lucro_desejado DECIMAL(10,2),
                tipo_lucro TEXT CHECK(tipo_lucro IN ('valor', 'percentual')) DEFAULT 'percentual',
                preco_venda DECIMAL(10,2),
                categoria TEXT,
                estoque_atual INTEGER DEFAULT 0,
                estoque_minimo INTEGER DEFAULT 0,
                foto TEXT,
                ativo BOOLEAN DEFAULT 1,
                data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
                data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Tabela de agendamentos
            `CREATE TABLE IF NOT EXISTS agendamentos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cliente_id INTEGER NOT NULL,
                produto_id INTEGER,
                vendedor_id INTEGER NOT NULL,
                data_visita DATE NOT NULL,
                hora_inicio TIME NOT NULL,
                hora_fim TIME NOT NULL,
                endereco_visita TEXT,
                observacoes TEXT,
                status TEXT DEFAULT 'agendado' CHECK(status IN ('agendado', 'confirmado', 'realizado', 'cancelado')),
                valor DECIMAL(10,2),
                data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
                data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (cliente_id) REFERENCES clientes (id),
                FOREIGN KEY (produto_id) REFERENCES produtos (id),
                FOREIGN KEY (vendedor_id) REFERENCES usuarios (id)
            )`,

            // Tabela de vendas
            `CREATE TABLE IF NOT EXISTS vendas (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cliente_id INTEGER NOT NULL,
                vendedor_id INTEGER NOT NULL,
                subtotal DECIMAL(10,2) NOT NULL,
                desconto_total DECIMAL(10,2) DEFAULT 0,
                total DECIMAL(10,2) NOT NULL,
                data_venda DATETIME DEFAULT CURRENT_TIMESTAMP,
                observacoes TEXT,
                FOREIGN KEY (cliente_id) REFERENCES clientes (id),
                FOREIGN KEY (vendedor_id) REFERENCES usuarios (id)
            )`,

            // Tabela de itens da venda
            `CREATE TABLE IF NOT EXISTS itens_venda (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                venda_id INTEGER NOT NULL,
                produto_id INTEGER NOT NULL,
                quantidade INTEGER NOT NULL,
                preco_unitario DECIMAL(10,2) NOT NULL,
                desconto DECIMAL(10,2) DEFAULT 0,
                subtotal DECIMAL(10,2) NOT NULL,
                FOREIGN KEY (venda_id) REFERENCES vendas (id),
                FOREIGN KEY (produto_id) REFERENCES produtos (id)
            )`,

            // Tabela de logs de auditoria
            `CREATE TABLE IF NOT EXISTS logs_auditoria (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                usuario_id INTEGER,
                acao TEXT NOT NULL,
                tabela TEXT NOT NULL,
                registro_id INTEGER,
                dados_anteriores TEXT,
                dados_novos TEXT,
                data_acao DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (usuario_id) REFERENCES usuarios (id)
            )`
        ];

        for (const sql of tables) {
            this.db.exec(sql);
        }
    }

function insertInitialData() {
    // Aguardar um pouco para garantir que as tabelas foram criadas
    setTimeout(() => {
        // Usuário administrador padrão
        const bcrypt = require('bcryptjs');
        const senhaAdmin = bcrypt.hashSync('admin123', 10);

        db.run(`INSERT OR IGNORE INTO usuarios (nome, email, senha, tipo) VALUES (?, ?, ?, ?)`,
            ['Administrador', '<EMAIL>', senhaAdmin, 'admin'], (err) => {
                if (err) {
                    console.error('Erro ao inserir usuário admin:', err);
                }
            });

        // Verificar se a tabela produtos existe antes de inserir dados
        db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='produtos'", (err, row) => {
            if (err) {
                console.error('Erro ao verificar tabela produtos:', err);
                return;
            }

            if (row) {
                // Produtos/Serviços iniciais (com novos campos)
                const produtos = [
                    ['Consulta Nutricional', 'Avaliação nutricional completa com plano alimentar personalizado', 50.00, 50.00, 'valor', 100.00, 'Nutrição', 10, 5, 60],
                    ['Suplementos Vitamínicos', 'Linha completa de vitaminas e minerais', 30.00, 59.90, 'valor', 89.90, 'Suplementação', 50, 10, 30],
                    ['Programa Detox', 'Programa completo de desintoxicação de 21 dias', 150.00, 149.00, 'valor', 299.00, 'Bem-estar', 20, 5, 45],
                    ['Avaliação Física', 'Avaliação corporal e plano de exercícios', 40.00, 80.00, 'valor', 120.00, 'Fitness', 15, 3, 90],
                    ['Produtos Naturais', 'Linha de produtos naturais e fitoterápicos', 25.00, 40.00, 'valor', 65.00, 'Fitoterapia', 30, 8, 30]
                ];

                const insertProduto = db.prepare(`INSERT OR IGNORE INTO produtos (nome, descricao, preco_custo, lucro_desejado, tipo_lucro, preco_venda, categoria, estoque_atual, estoque_minimo, duracao_estimada) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`);
                produtos.forEach(produto => {
                    insertProduto.run(produto, (err) => {
                        if (err) {
                            console.error('Erro ao inserir produto:', err);
                        }
                    });
                });
                insertProduto.finalize();
            }
        });

        // Funcionários iniciais
        const funcionarios = [
            ['Ana Silva', '<EMAIL>', '(11) 99999-1111', 'Consultora Sênior', '["Nutrição", "Suplementação"]'],
            ['Carlos Santos', '<EMAIL>', '(11) 99999-2222', 'Consultor', '["Fitness", "Bem-estar"]'],
            ['Maria Oliveira', '<EMAIL>', '(11) 99999-3333', 'Especialista', '["Fitoterapia", "Produtos Naturais"]']
        ];

        const insertFuncionario = db.prepare(`INSERT OR IGNORE INTO funcionarios (nome, email, telefone, cargo, especialidades) VALUES (?, ?, ?, ?, ?)`);
        funcionarios.forEach(funcionario => {
            insertFuncionario.run(funcionario, (err) => {
                if (err) {
                    console.error('Erro ao inserir funcionário:', err);
                }
            });
        });
        insertFuncionario.finalize();
    }, 1000); // Aguardar 1 segundo
}

// Executar inicialização se este arquivo for executado diretamente
if (require.main === module) {
    initializeDatabase();
    console.log('Banco de dados inicializado com sucesso!');
    db.close();
}

module.exports = { db, initializeDatabase };
