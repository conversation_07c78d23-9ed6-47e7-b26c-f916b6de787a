<?php
/**
 * Modelo de Agendamentos
 * Sistema Saúde Flex - Agendamentos
 */

require_once __DIR__ . '/BaseModel.php';

class Agendamento extends BaseModel {
    protected $table = 'agendamentos';
    
    /**
     * Buscar agendamentos com dados relacionados
     */
    public function findWithRelations($conditions = [], $orderBy = 'data_visita ASC, hora_inicio ASC') {
        $sql = "SELECT a.*, 
                       c.nome as cliente_nome, c.telefone as cliente_telefone,
                       f.nome as funcionario_nome, f.telefone as funcionario_telefone,
                       p.nome as produto_nome, p.categoria as produto_categoria
                FROM {$this->table} a
                LEFT JOIN clientes c ON a.cliente_id = c.id
                LEFT JOIN funcionarios f ON a.funcionario_id = f.id
                LEFT JOIN produtos p ON a.produto_id = p.id";
        
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                $whereClause[] = "a.{$field} = ?";
                $params[] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        return $this->query($sql, $params);
    }
    
    /**
     * Criar novo agendamento
     */
    public function create($data) {
        // Verificar conflitos de horário
        if ($this->hasConflict($data)) {
            return false;
        }
        
        $id = parent::create($data);
        
        if ($id) {
            $this->logAuditoria('CREATE', $id, null, $data);
        }
        
        return $id;
    }
    
    /**
     * Atualizar agendamento
     */
    public function update($id, $data) {
        $dadosAnteriores = $this->findById($id);
        
        // Verificar conflitos de horário (excluindo o próprio agendamento)
        if ($this->hasConflict($data, $id)) {
            return false;
        }
        
        $result = parent::update($id, $data);
        
        if ($result) {
            $this->logAuditoria('UPDATE', $id, $dadosAnteriores, $data);
        }
        
        return $result;
    }
    
    /**
     * Excluir agendamento
     */
    public function delete($id) {
        $dadosAnteriores = $this->findById($id);
        $result = parent::delete($id);
        
        if ($result) {
            $this->logAuditoria('DELETE', $id, $dadosAnteriores);
        }
        
        return $result;
    }
    
    /**
     * Verificar conflitos de horário
     */
    public function hasConflict($data, $excludeId = null) {
        if (!isset($data['funcionario_id']) || !isset($data['data_visita']) ||
            !isset($data['hora_inicio']) || !isset($data['hora_fim'])) {
            return false;
        }

        $sql = "SELECT COUNT(*) FROM {$this->table}
                WHERE funcionario_id = ?
                AND data_visita = ?
                AND status NOT IN ('cancelado')
                AND (
                    (hora_inicio <= ? AND hora_fim > ?) OR
                    (hora_inicio < ? AND hora_fim >= ?) OR
                    (hora_inicio >= ? AND hora_fim <= ?)
                )";

        $params = [
            $data['funcionario_id'],
            $data['data_visita'],
            $data['hora_inicio'], $data['hora_inicio'],
            $data['hora_fim'], $data['hora_fim'],
            $data['hora_inicio'], $data['hora_fim']
        ];

        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }

        return $this->queryValue($sql, $params) > 0;
    }
    
    /**
     * Buscar agendamentos por período
     */
    public function findByPeriodo($dataInicio, $dataFim, $funcionarioId = null) {
        $conditions = [];
        $params = [$dataInicio, $dataFim];
        
        $sql = "SELECT a.*, 
                       c.nome as cliente_nome, c.telefone as cliente_telefone,
                       f.nome as funcionario_nome,
                       p.nome as produto_nome
                FROM {$this->table} a
                LEFT JOIN clientes c ON a.cliente_id = c.id
                LEFT JOIN funcionarios f ON a.funcionario_id = f.id
                LEFT JOIN produtos p ON a.produto_id = p.id
                WHERE a.data_visita BETWEEN ? AND ?";
        
        if ($funcionarioId) {
            $sql .= " AND a.funcionario_id = ?";
            $params[] = $funcionarioId;
        }
        
        $sql .= " ORDER BY a.data_visita ASC, a.hora_inicio ASC";
        
        return $this->query($sql, $params);
    }
    
    /**
     * Buscar agendamentos de hoje
     */
    public function findHoje() {
        $hoje = date('Y-m-d');
        return $this->findByPeriodo($hoje, $hoje);
    }
    
    /**
     * Buscar próximos agendamentos
     */
    public function findProximos($limite = 10) {
        $sql = "SELECT a.*, 
                       c.nome as cliente_nome, c.telefone as cliente_telefone,
                       f.nome as funcionario_nome,
                       p.nome as produto_nome
                FROM {$this->table} a
                LEFT JOIN clientes c ON a.cliente_id = c.id
                LEFT JOIN funcionarios f ON a.funcionario_id = f.id
                LEFT JOIN produtos p ON a.produto_id = p.id
                WHERE a.data_visita >= CURDATE()
                AND a.status NOT IN ('cancelado', 'concluido')
                ORDER BY a.data_visita ASC, a.hora_inicio ASC
                LIMIT ?";
        
        return $this->query($sql, [$limite]);
    }
    
    /**
     * Atualizar status do agendamento
     */
    public function updateStatus($id, $status) {
        $statusValidos = ['agendado', 'confirmado', 'concluido', 'cancelado'];
        
        if (!in_array($status, $statusValidos)) {
            return false;
        }
        
        return $this->update($id, ['status' => $status]);
    }
    
    /**
     * Buscar agendamentos por cliente
     */
    public function findByCliente($clienteId) {
        return $this->findWithRelations(['cliente_id' => $clienteId], 'data_visita DESC, hora_inicio DESC');
    }
    
    /**
     * Buscar agendamentos por funcionário
     */
    public function findByFuncionario($funcionarioId, $dataInicio = null, $dataFim = null) {
        if ($dataInicio && $dataFim) {
            return $this->findByPeriodo($dataInicio, $dataFim, $funcionarioId);
        }
        
        return $this->findWithRelations(['funcionario_id' => $funcionarioId]);
    }
    
    /**
     * Validar dados do agendamento
     */
    public function validar($data, $isUpdate = false) {
        $rules = [
            'cliente_id' => ['required' => true, 'type' => 'numeric'],
            'funcionario_id' => ['required' => true, 'type' => 'numeric'],
            'data_visita' => ['required' => true, 'type' => 'date'],
            'hora_inicio' => ['required' => true, 'type' => 'time'],
            'hora_fim' => ['required' => true, 'type' => 'time'],
            'valor' => ['type' => 'numeric'],
            'status' => ['in' => ['agendado', 'confirmado', 'concluido', 'cancelado']]
        ];
        
        $errors = validateInput($data, $rules);
        
        // Validações específicas
        if (isset($data['data_visita']) && $data['data_visita'] < date('Y-m-d')) {
            $errors['data_visita'] = 'Data da visita não pode ser no passado';
        }
        
        if (isset($data['hora_inicio']) && isset($data['hora_fim'])) {
            if ($data['hora_inicio'] >= $data['hora_fim']) {
                $errors['hora_fim'] = 'Hora de fim deve ser posterior à hora de início';
            }
        }
        
        if (isset($data['valor']) && $data['valor'] < 0) {
            $errors['valor'] = 'Valor não pode ser negativo';
        }
        
        return $errors;
    }
    
    /**
     * Buscar estatísticas de agendamentos
     */
    public function getEstatisticas() {
        $stats = [];
        
        // Agendamentos hoje
        $stats['hoje'] = $this->count(['data_visita' => date('Y-m-d')]);
        
        // Agendamentos este mês
        $sql = "SELECT COUNT(*) FROM {$this->table} 
                WHERE MONTH(data_visita) = MONTH(CURRENT_DATE()) 
                AND YEAR(data_visita) = YEAR(CURRENT_DATE())";
        $stats['mes'] = $this->queryValue($sql);
        
        // Receita este mês
        $sql = "SELECT SUM(valor) FROM {$this->table} 
                WHERE MONTH(data_visita) = MONTH(CURRENT_DATE()) 
                AND YEAR(data_visita) = YEAR(CURRENT_DATE())
                AND status IN ('concluido', 'confirmado')";
        $stats['receita_mes'] = $this->queryValue($sql) ?: 0;
        
        // Distribuição por status
        $sql = "SELECT status, COUNT(*) as total FROM {$this->table} 
                WHERE data_visita >= CURDATE() 
                GROUP BY status";
        $statusData = $this->query($sql);
        
        foreach ($statusData as $status) {
            $stats['por_status'][$status['status']] = $status['total'];
        }
        
        // Funcionários com mais agendamentos este mês
        $sql = "SELECT f.nome, COUNT(a.id) as total 
                FROM funcionarios f 
                LEFT JOIN {$this->table} a ON f.id = a.funcionario_id 
                    AND MONTH(a.data_visita) = MONTH(CURRENT_DATE()) 
                    AND YEAR(a.data_visita) = YEAR(CURRENT_DATE())
                WHERE f.status = 'ativo' 
                GROUP BY f.id, f.nome 
                ORDER BY total DESC 
                LIMIT 5";
        $stats['funcionarios_top'] = $this->query($sql);
        
        return $stats;
    }
    
    /**
     * Buscar agendamentos para o calendário
     */
    public function getCalendarEvents($dataInicio, $dataFim) {
        $sql = "SELECT a.id, a.data_visita, a.hora_inicio, a.hora_fim, a.status,
                       c.nome as cliente_nome,
                       f.nome as funcionario_nome,
                       p.nome as produto_nome
                FROM {$this->table} a
                LEFT JOIN clientes c ON a.cliente_id = c.id
                LEFT JOIN funcionarios f ON a.funcionario_id = f.id
                LEFT JOIN produtos p ON a.produto_id = p.id
                WHERE a.data_visita BETWEEN ? AND ?
                ORDER BY a.data_visita ASC, a.hora_inicio ASC";
        
        $agendamentos = $this->query($sql, [$dataInicio, $dataFim]);
        
        // Formatar para o calendário
        $events = [];
        foreach ($agendamentos as $agendamento) {
            $events[] = [
                'id' => $agendamento['id'],
                'title' => $agendamento['cliente_nome'] . ' - ' . $agendamento['produto_nome'],
                'start' => $agendamento['data_visita'] . 'T' . $agendamento['hora_inicio'],
                'end' => $agendamento['data_visita'] . 'T' . $agendamento['hora_fim'],
                'backgroundColor' => $this->getStatusColor($agendamento['status']),
                'extendedProps' => [
                    'cliente' => $agendamento['cliente_nome'],
                    'funcionario' => $agendamento['funcionario_nome'],
                    'produto' => $agendamento['produto_nome'],
                    'status' => $agendamento['status']
                ]
            ];
        }
        
        return $events;
    }
    
    /**
     * Obter cor por status
     */
    private function getStatusColor($status) {
        $colors = [
            'agendado' => '#007bff',
            'confirmado' => '#28a745',
            'concluido' => '#6c757d',
            'cancelado' => '#dc3545'
        ];
        
        return $colors[$status] ?? '#007bff';
    }
}
