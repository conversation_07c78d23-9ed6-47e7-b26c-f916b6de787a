<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Saúde Flex</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .loading { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Teste do Sistema Saúde Flex</h1>
        
        <div class="test-section">
            <h3>1. Teste de Conexão com API</h3>
            <button onclick="testConnection()">Testar Conexão</button>
            <div id="connection-status" class="status loading">Aguardando teste...</div>
            <div id="connection-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. Teste de Login</h3>
            <button onclick="testLogin()">Testar Login (admin)</button>
            <div id="login-status" class="status loading">Aguardando teste...</div>
            <div id="login-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Teste de Dados - Clientes</h3>
            <button onclick="testClientes()">Carregar Clientes</button>
            <div id="clientes-status" class="status loading">Aguardando teste...</div>
            <div id="clientes-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. Teste de Dados - Produtos</h3>
            <button onclick="testProdutos()">Carregar Produtos</button>
            <div id="produtos-status" class="status loading">Aguardando teste...</div>
            <div id="produtos-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. Teste de Dados - Agendamentos</h3>
            <button onclick="testAgendamentos()">Carregar Agendamentos</button>
            <div id="agendamentos-status" class="status loading">Aguardando teste...</div>
            <div id="agendamentos-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>6. Teste Completo</h3>
            <button onclick="runAllTests()">Executar Todos os Testes</button>
            <div id="all-tests-status" class="status loading">Aguardando teste...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        let authToken = null;

        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = message;
        }

        function updateResult(elementId, data) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
        }

        async function testConnection() {
            updateStatus('connection-status', 'loading', 'Testando conexão...');
            
            try {
                const response = await fetch(`${API_BASE}/dashboard/stats`);
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('connection-status', 'success', 'Conexão OK');
                    updateResult('connection-result', data);
                } else {
                    updateStatus('connection-status', 'error', `Erro: ${response.status}`);
                    updateResult('connection-result', { error: response.statusText });
                }
            } catch (error) {
                updateStatus('connection-status', 'error', 'Erro de conexão');
                updateResult('connection-result', { error: error.message });
            }
        }

        async function testLogin() {
            updateStatus('login-status', 'loading', 'Testando login...');
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        senha: 'admin123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    updateStatus('login-status', 'success', 'Login realizado com sucesso');
                    updateResult('login-result', { 
                        user: data.user,
                        token: data.token ? 'Token recebido' : 'Sem token'
                    });
                } else {
                    const error = await response.json();
                    updateStatus('login-status', 'error', `Erro no login: ${response.status}`);
                    updateResult('login-result', error);
                }
            } catch (error) {
                updateStatus('login-status', 'error', 'Erro de conexão');
                updateResult('login-result', { error: error.message });
            }
        }

        async function testClientes() {
            updateStatus('clientes-status', 'loading', 'Carregando clientes...');
            
            try {
                const response = await fetch(`${API_BASE}/clientes`);
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('clientes-status', 'success', `${data.length} clientes carregados`);
                    updateResult('clientes-result', data);
                } else {
                    updateStatus('clientes-status', 'error', `Erro: ${response.status}`);
                    updateResult('clientes-result', { error: response.statusText });
                }
            } catch (error) {
                updateStatus('clientes-status', 'error', 'Erro de conexão');
                updateResult('clientes-result', { error: error.message });
            }
        }

        async function testProdutos() {
            updateStatus('produtos-status', 'loading', 'Carregando produtos...');
            
            try {
                const response = await fetch(`${API_BASE}/produtos`);
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('produtos-status', 'success', `${data.length} produtos carregados`);
                    updateResult('produtos-result', data);
                } else {
                    updateStatus('produtos-status', 'error', `Erro: ${response.status}`);
                    updateResult('produtos-result', { error: response.statusText });
                }
            } catch (error) {
                updateStatus('produtos-status', 'error', 'Erro de conexão');
                updateResult('produtos-result', { error: error.message });
            }
        }

        async function testAgendamentos() {
            updateStatus('agendamentos-status', 'loading', 'Carregando agendamentos...');
            
            try {
                const response = await fetch(`${API_BASE}/agendamentos`);
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('agendamentos-status', 'success', `${data.length} agendamentos carregados`);
                    updateResult('agendamentos-result', data);
                } else {
                    updateStatus('agendamentos-status', 'error', `Erro: ${response.status}`);
                    updateResult('agendamentos-result', { error: response.statusText });
                }
            } catch (error) {
                updateStatus('agendamentos-status', 'error', 'Erro de conexão');
                updateResult('agendamentos-result', { error: error.message });
            }
        }

        async function runAllTests() {
            updateStatus('all-tests-status', 'loading', 'Executando todos os testes...');
            
            await testConnection();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testLogin();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testClientes();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testProdutos();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAgendamentos();
            
            updateStatus('all-tests-status', 'success', 'Todos os testes concluídos');
        }
    </script>
</body>
</html>
