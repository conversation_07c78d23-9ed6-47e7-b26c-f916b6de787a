<?php
/**
 * Endpoints de Produtos
 * Sistema Saúde Flex - Agendamentos
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/JWT.php';
require_once __DIR__ . '/../classes/Produto.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';
$id = $_GET['id'] ?? null;

$produto = new Produto();

switch ($method) {
    case 'GET':
        if ($id) {
            getProduto($id);
        } elseif ($path === 'search') {
            searchProdutos();
        } elseif ($path === 'categorias') {
            getCategorias();
        } elseif ($path === 'estoque-baixo') {
            getEstoqueBaixo();
        } elseif ($path === 'stats') {
            getEstatisticas();
        } elseif ($path === 'mais-vendidos') {
            getMaisVendidos();
        } else {
            getProdutos();
        }
        break;
        
    case 'POST':
        createProduto();
        break;
        
    case 'PUT':
        if ($id) {
            if ($path === 'estoque') {
                updateEstoque($id);
            } else {
                updateProduto($id);
            }
        } else {
            jsonResponse(['error' => 'ID do produto é obrigatório'], 400);
        }
        break;
        
    case 'DELETE':
        if ($id) {
            deleteProduto($id);
        } else {
            jsonResponse(['error' => 'ID do produto é obrigatório'], 400);
        }
        break;
        
    default:
        jsonResponse(['error' => 'Método não permitido'], 405);
}

/**
 * Listar todos os produtos
 */
function getProdutos() {
    global $produto;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $categoria = $_GET['categoria'] ?? null;
    
    if ($categoria) {
        $produtos = $produto->findByCategoria($categoria);
    } else {
        $produtos = $produto->findAtivos();
    }
    
    jsonResponse(['produtos' => $produtos]);
}

/**
 * Buscar produto por ID
 */
function getProduto($id) {
    global $produto;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $produtoData = $produto->findById($id);
    
    if (!$produtoData) {
        jsonResponse(['error' => 'Produto não encontrado'], 404);
    }
    
    jsonResponse($produtoData);
}

/**
 * Criar novo produto
 */
function createProduto() {
    global $produto;
    
    // Verificar autenticação e permissão
    JWT::checkPermission(['admin', 'gerente']);
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Dados inválidos'], 400);
    }
    
    // Sanitizar dados
    $data = sanitizeInput($input);
    
    // Validar dados
    $errors = $produto->validar($data);
    
    if (!empty($errors)) {
        jsonResponse(['error' => 'Dados inválidos', 'details' => $errors], 400);
    }
    
    $id = $produto->create($data);
    
    if ($id) {
        jsonResponse(['id' => $id, 'message' => 'Produto cadastrado com sucesso!'], 201);
    } else {
        jsonResponse(['error' => 'Erro ao cadastrar produto'], 500);
    }
}

/**
 * Atualizar produto
 */
function updateProduto($id) {
    global $produto;
    
    // Verificar autenticação e permissão
    JWT::checkPermission(['admin', 'gerente']);
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Dados inválidos'], 400);
    }
    
    // Verificar se produto existe
    if (!$produto->findById($id)) {
        jsonResponse(['error' => 'Produto não encontrado'], 404);
    }
    
    // Sanitizar dados
    $data = sanitizeInput($input);
    
    // Validar dados
    $errors = $produto->validar($data, true);
    
    if (!empty($errors)) {
        jsonResponse(['error' => 'Dados inválidos', 'details' => $errors], 400);
    }
    
    $result = $produto->update($id, $data);
    
    if ($result) {
        jsonResponse(['message' => 'Produto atualizado com sucesso!']);
    } else {
        jsonResponse(['error' => 'Erro ao atualizar produto'], 500);
    }
}

/**
 * Excluir produto
 */
function deleteProduto($id) {
    global $produto;
    
    // Verificar autenticação e permissão
    JWT::checkPermission(['admin', 'gerente']);
    
    // Verificar se produto existe
    if (!$produto->findById($id)) {
        jsonResponse(['error' => 'Produto não encontrado'], 404);
    }
    
    $result = $produto->delete($id);
    
    if ($result) {
        jsonResponse(['message' => 'Produto removido com sucesso!']);
    } else {
        jsonResponse(['error' => 'Erro ao remover produto'], 500);
    }
}

/**
 * Pesquisar produtos
 */
function searchProdutos() {
    global $produto;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $termo = $_GET['q'] ?? '';
    
    if (empty($termo)) {
        jsonResponse(['error' => 'Termo de pesquisa é obrigatório'], 400);
    }
    
    $resultados = $produto->search($termo);
    
    jsonResponse(['produtos' => $resultados]);
}

/**
 * Obter categorias disponíveis
 */
function getCategorias() {
    global $produto;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $categorias = $produto->getCategorias();
    
    jsonResponse(['categorias' => $categorias]);
}

/**
 * Obter produtos com estoque baixo
 */
function getEstoqueBaixo() {
    global $produto;
    
    // Verificar autenticação e permissão
    JWT::checkPermission(['admin', 'gerente']);
    
    $produtos = $produto->findEstoqueBaixo();
    
    jsonResponse(['produtos' => $produtos]);
}

/**
 * Atualizar estoque do produto
 */
function updateEstoque($id) {
    global $produto;
    
    // Verificar autenticação e permissão
    JWT::checkPermission(['admin', 'gerente']);
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Dados inválidos'], 400);
    }
    
    $quantidade = $input['quantidade'] ?? 0;
    $operacao = $input['operacao'] ?? 'subtrair'; // 'subtrair' ou 'adicionar'
    
    if (!is_numeric($quantidade) || $quantidade <= 0) {
        jsonResponse(['error' => 'Quantidade deve ser um número positivo'], 400);
    }
    
    if (!in_array($operacao, ['subtrair', 'adicionar'])) {
        jsonResponse(['error' => 'Operação deve ser "subtrair" ou "adicionar"'], 400);
    }
    
    // Verificar se produto existe
    if (!$produto->findById($id)) {
        jsonResponse(['error' => 'Produto não encontrado'], 404);
    }
    
    $result = $produto->atualizarEstoque($id, $quantidade, $operacao);
    
    if ($result) {
        jsonResponse(['message' => 'Estoque atualizado com sucesso!']);
    } else {
        jsonResponse(['error' => 'Erro ao atualizar estoque'], 500);
    }
}

/**
 * Obter estatísticas de produtos
 */
function getEstatisticas() {
    global $produto;
    
    // Verificar autenticação e permissão
    JWT::checkPermission(['admin', 'gerente']);
    
    $stats = $produto->getEstatisticas();
    
    jsonResponse($stats);
}

/**
 * Obter produtos mais vendidos
 */
function getMaisVendidos() {
    global $produto;
    
    // Verificar autenticação
    JWT::authenticate();
    
    $limite = $_GET['limite'] ?? 10;
    $produtos = $produto->getMaisVendidos($limite);
    
    jsonResponse(['produtos' => $produtos]);
}
