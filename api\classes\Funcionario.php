<?php
/**
 * Modelo de Funcionários
 * Sistema Saúde Flex - Agendamentos
 */

require_once __DIR__ . '/BaseModel.php';

class Funcionario extends BaseModel {
    protected $table = 'funcionarios';
    
    /**
     * Buscar funcionários ativos
     */
    public function findAtivos() {
        return $this->findAll(['status' => 'ativo'], 'nome ASC');
    }
    
    /**
     * Criar novo funcionário
     */
    public function create($data) {
        // Converter especialidades para JSON se for array
        if (isset($data['especialidades']) && is_array($data['especialidades'])) {
            $data['especialidades'] = json_encode($data['especialidades']);
        }
        
        $id = parent::create($data);
        
        if ($id) {
            $this->logAuditoria('CREATE', $id, null, $data);
        }
        
        return $id;
    }
    
    /**
     * Atualizar funcionário
     */
    public function update($id, $data) {
        $dadosAnteriores = $this->findById($id);
        
        // Converter especialidades para JSON se for array
        if (isset($data['especialidades']) && is_array($data['especialidades'])) {
            $data['especialidades'] = json_encode($data['especialidades']);
        }
        
        $result = parent::update($id, $data);
        
        if ($result) {
            $this->logAuditoria('UPDATE', $id, $dadosAnteriores, $data);
        }
        
        return $result;
    }
    
    /**
     * Excluir funcionário (soft delete)
     */
    public function delete($id) {
        $dadosAnteriores = $this->findById($id);
        $result = $this->softDelete($id);
        
        if ($result) {
            $this->logAuditoria('DELETE', $id, $dadosAnteriores);
        }
        
        return $result;
    }
    
    /**
     * Buscar funcionário por email
     */
    public function findByEmail($email) {
        return $this->findOne(['email' => $email, 'status' => 'ativo']);
    }
    
    /**
     * Buscar funcionários por especialidade
     */
    public function findByEspecialidade($especialidade) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'ativo' 
                AND JSON_CONTAINS(especialidades, ?) 
                ORDER BY nome ASC";
        
        return $this->query($sql, [json_encode($especialidade)]);
    }
    
    /**
     * Buscar funcionários por termo de pesquisa
     */
    public function search($termo) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'ativo' 
                AND (nome LIKE ? OR email LIKE ? OR cargo LIKE ?) 
                ORDER BY nome ASC";
        
        $termoBusca = "%{$termo}%";
        return $this->query($sql, [$termoBusca, $termoBusca, $termoBusca]);
    }
    
    /**
     * Verificar se email já existe
     */
    public function emailExists($email, $excludeId = null) {
        if (empty($email)) {
            return false;
        }
        
        $conditions = ['email' => $email, 'status' => 'ativo'];
        
        if ($excludeId) {
            $sql = "SELECT COUNT(*) FROM {$this->table} WHERE email = ? AND status = 'ativo' AND id != ?";
            return $this->queryValue($sql, [$email, $excludeId]) > 0;
        }
        
        return $this->count($conditions) > 0;
    }
    
    /**
     * Buscar funcionários disponíveis em uma data/hora
     */
    public function findDisponiveis($data, $horaInicio, $horaFim) {
        $sql = "SELECT f.* FROM {$this->table} f 
                WHERE f.status = 'ativo' 
                AND f.id NOT IN (
                    SELECT a.funcionario_id 
                    FROM agendamentos a 
                    WHERE a.data_visita = ? 
                    AND a.status NOT IN ('cancelado') 
                    AND (
                        (a.hora_inicio <= ? AND a.hora_fim > ?) OR
                        (a.hora_inicio < ? AND a.hora_fim >= ?) OR
                        (a.hora_inicio >= ? AND a.hora_fim <= ?)
                    )
                ) 
                ORDER BY f.nome ASC";
        
        return $this->query($sql, [
            $data, 
            $horaInicio, $horaInicio,
            $horaFim, $horaFim,
            $horaInicio, $horaFim
        ]);
    }
    
    /**
     * Buscar agenda do funcionário
     */
    public function getAgenda($funcionarioId, $dataInicio = null, $dataFim = null) {
        $dataInicio = $dataInicio ?: date('Y-m-d');
        $dataFim = $dataFim ?: date('Y-m-d', strtotime('+30 days'));
        
        $sql = "SELECT a.*, c.nome as cliente_nome, p.nome as produto_nome 
                FROM agendamentos a 
                LEFT JOIN clientes c ON a.cliente_id = c.id 
                LEFT JOIN produtos p ON a.produto_id = p.id 
                WHERE a.funcionario_id = ? 
                AND a.data_visita BETWEEN ? AND ? 
                ORDER BY a.data_visita ASC, a.hora_inicio ASC";
        
        return $this->query($sql, [$funcionarioId, $dataInicio, $dataFim]);
    }
    
    /**
     * Validar dados do funcionário
     */
    public function validar($data, $isUpdate = false) {
        $rules = [
            'nome' => ['required' => true, 'max_length' => 255],
            'email' => ['type' => 'email', 'max_length' => 255],
            'telefone' => ['max_length' => 20],
            'cargo' => ['max_length' => 100]
        ];
        
        $errors = validateInput($data, $rules);
        
        // Verificar se email já existe (se fornecido)
        if (isset($data['email']) && !empty($data['email'])) {
            $excludeId = $isUpdate && isset($data['id']) ? $data['id'] : null;
            if ($this->emailExists($data['email'], $excludeId)) {
                $errors['email'] = 'Este email já está em uso';
            }
        }
        
        // Validar especialidades se fornecidas
        if (isset($data['especialidades'])) {
            if (is_string($data['especialidades'])) {
                $especialidades = json_decode($data['especialidades'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $errors['especialidades'] = 'Formato de especialidades inválido';
                }
            } elseif (!is_array($data['especialidades'])) {
                $errors['especialidades'] = 'Especialidades deve ser um array';
            }
        }
        
        return $errors;
    }
    
    /**
     * Buscar estatísticas de funcionários
     */
    public function getEstatisticas() {
        $stats = [];
        
        // Total de funcionários ativos
        $stats['total_ativos'] = $this->count(['status' => 'ativo']);
        
        // Funcionários cadastrados este mês
        $sql = "SELECT COUNT(*) FROM {$this->table} 
                WHERE status = 'ativo' 
                AND MONTH(data_cadastro) = MONTH(CURRENT_DATE()) 
                AND YEAR(data_cadastro) = YEAR(CURRENT_DATE())";
        $stats['cadastrados_mes'] = $this->queryValue($sql);
        
        // Distribuição por cargo
        $sql = "SELECT cargo, COUNT(*) as total FROM {$this->table} 
                WHERE status = 'ativo' AND cargo IS NOT NULL 
                GROUP BY cargo 
                ORDER BY total DESC";
        $stats['por_cargo'] = $this->query($sql);
        
        // Funcionários com mais agendamentos este mês
        $sql = "SELECT f.nome, COUNT(a.id) as total_agendamentos 
                FROM {$this->table} f 
                LEFT JOIN agendamentos a ON f.id = a.funcionario_id 
                    AND MONTH(a.data_visita) = MONTH(CURRENT_DATE()) 
                    AND YEAR(a.data_visita) = YEAR(CURRENT_DATE())
                    AND a.status NOT IN ('cancelado')
                WHERE f.status = 'ativo' 
                GROUP BY f.id, f.nome 
                ORDER BY total_agendamentos DESC 
                LIMIT 5";
        $stats['mais_agendamentos'] = $this->query($sql);
        
        return $stats;
    }
    
    /**
     * Buscar todas as especialidades disponíveis
     */
    public function getAllEspecialidades() {
        $sql = "SELECT DISTINCT especialidades FROM {$this->table} 
                WHERE status = 'ativo' AND especialidades IS NOT NULL";
        
        $result = $this->query($sql);
        $especialidades = [];
        
        foreach ($result as $row) {
            $esp = json_decode($row['especialidades'], true);
            if (is_array($esp)) {
                $especialidades = array_merge($especialidades, $esp);
            }
        }
        
        return array_unique($especialidades);
    }
    
    /**
     * Buscar funcionários com dados formatados
     */
    public function findWithFormattedData() {
        $funcionarios = $this->findAtivos();
        
        foreach ($funcionarios as &$funcionario) {
            // Decodificar especialidades JSON
            if ($funcionario['especialidades']) {
                $funcionario['especialidades'] = json_decode($funcionario['especialidades'], true);
            } else {
                $funcionario['especialidades'] = [];
            }
        }
        
        return $funcionarios;
    }
}
